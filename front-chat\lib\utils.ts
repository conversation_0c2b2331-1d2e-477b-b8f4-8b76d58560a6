import { Chat } from "@/lib/db/schema"
import { CoreMessage, generateId, Message } from "ai"
import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

import type { Message as DBMessage } from '@/lib/db/schema';
/**
 * Merge tailwind
 * @param inputs
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatDate(input: string | number | Date): string {
  const date = new Date(input)
  return date.toLocaleDateString('korean', {
    month: 'long',
    day: 'numeric',
    year: 'numeric'
  })
}

interface ApplicationError extends Error {
  info: string;
  status: number;
}

export const fetcher = async (url: string) => {
  const res = await fetch(url);

  if (!res.ok) {
    // 401 에러 시 로그인 페이지로 리다이렉트
    if (res.status === 401) {
      // 클라이언트 사이드에서만 리다이렉트 실행
      if (typeof window !== 'undefined') {
        const currentUrl = window.location.pathname + window.location.search;
        window.location.href = `/login?callbackUrl=${encodeURIComponent(currentUrl)}`;
        return; // 리다이렉트 후 함수 종료
      }
    }

    const error = new Error(
      'An error occurred while fetching the data.'
    ) as ApplicationError;

    error.info = await res.json();
    error.status = res.status;

    throw error;
  }

  return res.json();
};

export function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0;
    const v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}



export function convertToUIMessages(
  messages: Array<DBMessage>,
): Array<Message> {
  // Vercel AI Chatbot 예제 패턴: parts 기반으로 메시지 변환
  return messages.map((message) => {
    // parts 필드가 문자열인 경우 JSON 파싱 시도
    let parsedParts: Message['parts'];
    try {
      if (typeof message.parts === 'string') {
        parsedParts = JSON.parse(message.parts);
      } else {
        parsedParts = message.parts as Message['parts'];
      }
    } catch (error) {
      console.warn('Failed to parse message parts:', error);
      parsedParts = [];
    }

    // attachments 필드도 동일하게 처리
    let parsedAttachments: Array<any>;
    try {
      if (typeof message.attachments === 'string') {
        parsedAttachments = JSON.parse(message.attachments);
      } else {
        parsedAttachments = (message.attachments as Array<any>) ?? [];
      }
    } catch (error) {
      console.warn('Failed to parse message attachments:', error);
      parsedAttachments = [];
    }

    return {
      id: message.id,
      parts: parsedParts,
      role: message.role as Message['role'],
      // Note: content will soon be deprecated in @ai-sdk/react
      content: '',
      createdAt: message.createdAt,
      experimental_attachments: parsedAttachments,
    };
  });
}

export function getMostRecentUserMessage(messages: Array<CoreMessage>) {
  const userMessages = messages.filter((message) => message.role === 'user');
  return userMessages.at(-1);
}

export function getMessageIdFromAnnotations(message: Message) {
  if (!message.annotations) return message.id;

  const [annotation] = message.annotations;
  if (!annotation) return message.id;

  // @ts-expect-error messageIdFromServer is not defined in MessageAnnotation
  return annotation.messageIdFromServer;
}



export const defaultSystemMessage = ``
export const defaultModelId = '2JgxF9fviqd4cezxuT4UAuJCcRP2';
export const defaultChatbotId = 'chbt_CRNiaMw';
export const V2ModelId = 'Lk5521ILdWe9t18yi0zcJKU0teE3';
export const V2ChatbotId = 'chbt_ahiNhfU';

export const prunedMessages = (messages: Message[]): Message[] => {
  // 1. 마지막 4개 메시지만 선택
  const recentMessages = messages.slice(-4);

  // 2. Human-in-the-loop 도구 호출 후 사용자가 tool-result 없이 채팅한 경우 처리
  const processedMessages = recentMessages.map((message, index) => {
    // Assistant 메시지에서 미완료된 tool-invocation 확인 (parts 배열 사용)
    if (message.role === 'assistant' && Array.isArray(message.parts)) {
      const hasIncompleteToolCall = message.parts.some((part: any) =>
        part.type === 'tool-invocation' &&
        part.toolInvocation?.state === 'call' &&
        !part.toolInvocation?.result
      );

      // 미완료된 tool-invocation이 있고, 다음 메시지가 user 메시지인 경우
      if (hasIncompleteToolCall && index < recentMessages.length - 1) {
        const nextMessage = recentMessages[index + 1];
        if (nextMessage?.role === 'user') {
          // 미완료된 tool-invocation을 제거하고 텍스트 응답만 유지
          const filteredParts = message.parts.filter((part: any) =>
            part.type !== 'tool-invocation' ||
            (part.type === 'tool-invocation' && part.toolInvocation?.result)
          );

          // 텍스트 응답이 없으면 기본 응답 추가
          if (filteredParts.length === 0 || !filteredParts.some((p: any) => p.type === 'text')) {
            filteredParts.unshift({
              type: 'text',
              text: '요청을 처리하고 있습니다. 추가로 도움이 필요한 것이 있나요?'
            });
          }

          return { ...message, parts: filteredParts };
        }
      }

      // 3. 도구 결과에서 불필요한 대용량 필드 제거
      const cleanedParts = message.parts.map((part: any) => {
        if (part.type === 'tool-invocation' && part.toolInvocation?.state === 'result') {
          const toolName = part.toolInvocation?.toolName;
          const result = part.toolInvocation.result;

          // searchAddress, searchOrigin, searchDestination: geom, buildGeom 필드 제거
          if (['searchAddress', 'searchOrigin', 'searchDestination'].includes(toolName)) {
            if (result && typeof result === 'object' && result.result?.jusoList) {
              // jusoList 배열의 각 항목에서 geom, buildGeom 필드 제거
              const cleanedJusoList = result.result.jusoList.map((item: any) => {
                const { geom, buildGeom, ...cleanedItem } = item;
                return cleanedItem;
              });

              return {
                ...part,
                toolInvocation: {
                  ...part.toolInvocation,
                  result: {
                    ...result,
                    result: {
                      ...result.result,
                      jusoList: cleanedJusoList
                    }
                  }
                }
              };
            }
          }

          // searchDirections: sections 필드 제거 (상세 경로 데이터)
          if (toolName === 'searchDirections') {
            if (result && typeof result === 'object' && result.routes) {
              const cleanedRoutes = result.routes.map((route: any) => {
                const { sections, ...cleanedRoute } = route;
                return cleanedRoute;
              });

              return {
                ...part,
                toolInvocation: {
                  ...part.toolInvocation,
                  result: {
                    ...result,
                    routes: cleanedRoutes
                  }
                }
              };
            }
          }

          // densityAnalysis: features 필드 제거 (GeoJSON 형상 데이터)
          if (toolName === 'performDensityAnalysis') {
            if (result && typeof result === 'object') {
              const { features, ...cleanedResult } = result;

              return {
                ...part,
                toolInvocation: {
                  ...part.toolInvocation,
                  result: {
                    ...cleanedResult,
                    // features 배열 크기만 유지 (실제 데이터는 제거)
                    featuresCount: Array.isArray(features) ? features.length : 0
                  }
                }
              };
            }
          }

        }
        return part;
      });

      return { ...message, parts: cleanedParts };
    }

    return message;
  });

  return processedMessages;
};
