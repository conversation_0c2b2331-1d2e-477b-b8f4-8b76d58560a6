// Define your models here.

export interface ModelCapabilities {
  reasoning: boolean;
  streaming: boolean;
  tools: boolean;
  vision: boolean;
}

export interface Model {
  id: string;
  label: string;
  apiIdentifier: string;
  description: string;
  provider: "geon" | "openai" | "dify";
  capabilities: ModelCapabilities;
}

export const models: Array<Model> = [
  {
    id: "Qwen3-14B",
    label: "Qwen3 14B",
    apiIdentifier: "Qwen/Qwen2.5-14B",
    description: "Qwen3 14B 모델입니다.",
    provider: "geon",
    capabilities: {
      reasoning: false,
      streaming: true,
      tools: true,
      vision: false,
    },
  },
  {
    id: "Qwen3-4B",
    label: "Qwen3 (추론)",
    apiIdentifier: "Qwen/Qwen3-4B",
    description: "Qwen3-4B 추론 모델입니다.",
    provider: "geon",
    capabilities: {
      reasoning: true,
      streaming: true,
      tools: true,
      vision: false,
    },
  },
  {
    id: "gpt-4.1-mini",
    label: "GPT 4.1 mini",
    apiIdentifier: "gpt-4.1-mini",
    description: "OpenAI의 GPT 4.1 mini 모델입니다.",
    provider: "openai",
    capabilities: {
      reasoning: false,
      streaming: true,
      tools: true,
      vision: false,
    },
  },
] as const;

export const DEFAULT_MODEL_NAME: string = "Qwen3-14B";

// 모델 유틸리티 함수들
export function getModelById(modelId: string): Model {
  return models.find((model) => model.id === modelId) || models[0];
}

export function getModelCapabilities(
  modelId: string
): ModelCapabilities | undefined {
  const model = getModelById(modelId);
  return model?.capabilities;
}

export function supportsReasoning(modelId: string): boolean {
  const capabilities = getModelCapabilities(modelId);
  return capabilities?.reasoning ?? false;
}

export function supportsStreaming(modelId: string): boolean {
  const capabilities = getModelCapabilities(modelId);
  return capabilities?.streaming ?? false;
}

export function supportsTools(modelId: string): boolean {
  const capabilities = getModelCapabilities(modelId);
  return capabilities?.tools ?? false;
}

export function supportsVision(modelId: string): boolean {
  const capabilities = getModelCapabilities(modelId);
  return capabilities?.vision ?? false;
}

export function getModelProvider(
  modelId: string
): "geon" | "openai" | "dify" | undefined {
  const model = getModelById(modelId);
  return model?.provider;
}

export function getReasoningDisabledMessage(
  modelId: string
): string | undefined {
  const model = getModelById(modelId);
  if (!model || model.capabilities.reasoning) {
    return undefined;
  }

  // 모델별 맞춤 메시지
  switch (model.id) {
    case "gpt-4.1-mini":
      return "GPT 4.1 Nano 에서 지원되지 않습니다.";
    default:
      return "현재 선택된 모델은 추론 기능을 지원하지 않습니다";
  }
}
