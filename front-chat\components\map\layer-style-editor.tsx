"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Palette, Circle, Square, Triangle, Star, Plus, X } from "lucide-react";
import type { LayerProps } from "@geon-map/odf";
import { 
  WMSLayerStyle, 
  WFSLayerStyle, 
  LayerStyle,
  createDefaultPointStyle,
  createDefaultLineStyle,
  createDefaultPolygonStyle,
  createDefaultWFSPointStyle,
  createDefaultWFSLineStyle,
  createDefaultWFSPolygonStyle,
  MarkSymbolizer,
  LineSymbolizer,
  FillSymbolizer
} from "@/types/layer-style";

// 필터 표시를 위한 헬퍼 함수
const formatFilterDisplay = (filter: any): string => {
  if (!Array.isArray(filter)) {
    return 'Filter';
  }

  const [operator, column, value] = filter;

  // 연산자 한글 변환
  const operatorMap: Record<string, string> = {
    '==': '=',
    '!=': '≠',
    '>': '>',
    '<': '<',
    '>=': '≥',
    '<=': '≤',
    '*=': '포함',
    '&&': '그리고',
    '||': '또는'
  };

  // 복합 조건 처리
  if (operator === '&&' || operator === '||') {
    const condition1 = Array.isArray(column) ? formatFilterDisplay(column) : column;
    const condition2 = Array.isArray(value) ? formatFilterDisplay(value) : value;
    return `${condition1} ${operatorMap[operator]} ${condition2}`;
  }

  // 단일 조건 처리
  const operatorText = operatorMap[operator] || operator;
  const columnText = typeof column === 'string' ? column : String(column);
  const valueText = value === null ? 'null' : String(value);

  // 특별한 경우들 처리
  if (operator === '*=') {
    return `${columnText} 포함 "${valueText}"`;
  }

  if (value === null) {
    return operator === '==' ? `${columnText} 없음` : `${columnText} 있음`;
  }

  // 숫자 범위 표현 개선
  if (typeof value === 'number') {
    if (operator === '>') return `${columnText} > ${valueText}`;
    if (operator === '<') return `${columnText} < ${valueText}`;
    if (operator === '>=') return `${columnText} ≥ ${valueText}`;
    if (operator === '<=') return `${columnText} ≤ ${valueText}`;
  }

  return `${columnText} ${operatorText} ${valueText}`;
};

interface LayerStyleEditorProps {
  layer: LayerProps;
  onStyleChange: (style: LayerStyle) => void;
}

export function LayerStyleEditor({ layer, onStyleChange }: LayerStyleEditorProps) {
  const [isOpen, setIsOpen] = useState(false);

  // 레이어 타입과 서비스 타입 확인
  const isWMSLayer = (layer as any).service === 'wms';
  const isWFSLayer = (layer as any).service === 'wfs';
  const geometryType = (layer as any).geometryType || 'point';





  // 단일 스타일로 변경 핸들러
  const handleConvertToSingleStyle = () => {
    const defaultStyle = (() => {
      switch (geometryType) {
        case 'point':
        case '1':
          return createDefaultPointStyle();
        case 'line':
        case '2':
          return createDefaultLineStyle();
        case 'polygon':
        case '3':
          return createDefaultPolygonStyle();
        default:
          return createDefaultPointStyle();
      }
    })();

    handleStyleChange(defaultStyle);
  };

  // 스타일 초기화 핸들러
  const handleResetStyle = () => {
    const defaultStyle = getCurrentStyle();
    handleStyleChange(defaultStyle);
  };
  
  // 현재 스타일 가져오기
  const getCurrentStyle = (): LayerStyle => {
    const currentStyle = (layer as any).renderOptions?.style || (layer as any).style;
    
    if (currentStyle) {
      return currentStyle;
    }
    
    // 기본 스타일 생성
    if (isWMSLayer) {
      switch (geometryType) {
        case 'point':
        case '1':
          return createDefaultPointStyle();
        case 'line':
        case '2':
          return createDefaultLineStyle();
        case 'polygon':
        case '3':
          return createDefaultPolygonStyle();
        default:
          return createDefaultPointStyle();
      }
    } else if (isWFSLayer) {
      switch (geometryType) {
        case 'point':
        case '1':
          return createDefaultWFSPointStyle();
        case 'line':
        case '2':
          return createDefaultWFSLineStyle();
        case 'polygon':
        case '3':
          return createDefaultWFSPolygonStyle();
        default:
          return createDefaultWFSPointStyle();
      }
    }
    
    return createDefaultPointStyle();
  };

  const [currentStyle, setCurrentStyle] = useState<LayerStyle>(getCurrentStyle());

  const handleStyleChange = (newStyle: LayerStyle) => {
    setCurrentStyle(newStyle);
    onStyleChange(newStyle);
  };

  // 심볼 타입 아이콘 매핑
  const getSymbolIcon = (symbolType: string) => {
    switch (symbolType) {
      case 'circle':
        return <Circle size={16} />;
      case 'square':
        return <Square size={16} />;
      case 'triangle':
        return <Triangle size={16} />;
      case 'star':
        return <Star size={16} />;
      case 'cross':
        return <Plus size={16} />;
      case 'x':
        return <X size={16} />;
      default:
        return <Circle size={16} />;
    }
  };

  // 지오메트리 타입 한국어 변환
  const getGeometryTypeText = (type: string) => {
    switch (type) {
      case 'point':
      case '1':
        return '포인트';
      case 'line':
      case '2':
        return '라인';
      case 'polygon':
      case '3':
        return '폴리곤';
      default:
        return '알 수 없음';
    }
  };

  const renderWMSStyleEditor = (style: WMSLayerStyle) => {
    // 여러 rule이 있는 경우 (유형별 스타일)
    if (style.rules.length > 1) {
      return (
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Label className="text-sm font-medium">유형별 스타일</Label>
            <Badge variant="secondary" className="text-xs">
              {style.rules.length}개 규칙
            </Badge>
          </div>

          {style.rules.map((rule, index) => {
            const symbolizer = rule.symbolizers[0];

            // 심볼라이저 업데이트 헬퍼 함수
            const updateRuleSymbolizer = (updates: any) => {
              const updatedSymbolizer = { ...symbolizer, ...updates };
              const newStyle: WMSLayerStyle = {
                ...style,
                rules: style.rules.map((r, i) =>
                  i === index
                    ? { ...r, symbolizers: [updatedSymbolizer] }
                    : r
                )
              };
              handleStyleChange(newStyle);
            };

            return (
              <Card key={index} className="mb-3">
                <CardContent className="p-4">
                  {/* 규칙 헤더 */}
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium">{rule.name}</span>
                    </div>
                    {rule.filter && (
                      <Badge variant="outline" className="text-xs">
                        {formatFilterDisplay(rule.filter)}
                      </Badge>
                    )}
                  </div>

                  {/* Mark 심볼라이저 편집 */}
                  {symbolizer.kind === 'Mark' && (() => {
                    const markSymbolizer = symbolizer as MarkSymbolizer;
                    return (
                      <div className="space-y-3">
                        {/* 색상 및 크기 */}
                        <div className="grid grid-cols-2 gap-3">
                          <div className="space-y-2">
                            <Label className="text-xs text-muted-foreground">색상</Label>
                            <Input
                              type="color"
                              value={markSymbolizer.color}
                              onChange={(e) => updateRuleSymbolizer({ color: e.target.value })}
                              className="h-8 w-full cursor-pointer"
                            />
                          </div>
                          <div className="space-y-2">
                            <div className="flex items-center justify-between">
                              <Label className="text-xs text-muted-foreground">크기</Label>
                              <Badge variant="outline" className="text-xs">
                                {markSymbolizer.radius}px
                              </Badge>
                            </div>
                            <Slider
                              value={[markSymbolizer.radius]}
                              onValueChange={([value]) => updateRuleSymbolizer({ radius: value })}
                              min={1}
                              max={20}
                              step={1}
                              className="w-full"
                            />
                          </div>
                        </div>

                        {/* 투명도 */}
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <Label className="text-xs text-muted-foreground">투명도</Label>
                            <Badge variant="outline" className="text-xs">
                              {Math.round(markSymbolizer.fillOpacity * 100)}%
                            </Badge>
                          </div>
                          <Slider
                            value={[markSymbolizer.fillOpacity]}
                            onValueChange={([value]) => updateRuleSymbolizer({ fillOpacity: value })}
                            min={0}
                            max={1}
                            step={0.1}
                            className="w-full"
                          />
                        </div>

                        {/* 윤곽선 */}
                        {markSymbolizer.strokeColor && (
                          <div className="grid grid-cols-2 gap-3">
                            <div className="space-y-2">
                              <Label className="text-xs text-muted-foreground">윤곽선 색상</Label>
                              <Input
                                type="color"
                                value={markSymbolizer.strokeColor}
                                onChange={(e) => updateRuleSymbolizer({ strokeColor: e.target.value })}
                                className="h-8 w-full cursor-pointer"
                              />
                            </div>
                            <div className="space-y-2">
                              <div className="flex items-center justify-between">
                                <Label className="text-xs text-muted-foreground">윤곽선 두께</Label>
                                <Badge variant="outline" className="text-xs">
                                  {markSymbolizer.strokeWidth || 1}px
                                </Badge>
                              </div>
                              <Slider
                                value={[markSymbolizer.strokeWidth || 1]}
                                onValueChange={([value]) => updateRuleSymbolizer({ strokeWidth: value })}
                                min={0}
                                max={10}
                                step={1}
                                className="w-full"
                              />
                            </div>
                          </div>
                        )}
                      </div>
                    );
                  })()}

                  {/* Line 심볼라이저 편집 */}
                  {symbolizer.kind === 'Line' && (() => {
                    const lineSymbolizer = symbolizer as LineSymbolizer;
                    return (
                      <div className="space-y-3">
                        <div className="grid grid-cols-2 gap-3">
                          <div className="space-y-2">
                            <Label className="text-xs text-muted-foreground">색상</Label>
                            <Input
                              type="color"
                              value={lineSymbolizer.color}
                              onChange={(e) => updateRuleSymbolizer({ color: e.target.value })}
                              className="h-8 w-full cursor-pointer"
                            />
                          </div>
                          <div className="space-y-2">
                            <div className="flex items-center justify-between">
                              <Label className="text-xs text-muted-foreground">두께</Label>
                              <Badge variant="outline" className="text-xs">
                                {lineSymbolizer.width}px
                              </Badge>
                            </div>
                            <Slider
                              value={[lineSymbolizer.width]}
                              onValueChange={([value]) => updateRuleSymbolizer({ width: value })}
                              min={1}
                              max={10}
                              step={1}
                              className="w-full"
                            />
                          </div>
                        </div>
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <Label className="text-xs text-muted-foreground">투명도</Label>
                            <Badge variant="outline" className="text-xs">
                              {Math.round(lineSymbolizer.opacity * 100)}%
                            </Badge>
                          </div>
                          <Slider
                            value={[lineSymbolizer.opacity]}
                            onValueChange={([value]) => updateRuleSymbolizer({ opacity: value })}
                            min={0}
                            max={1}
                            step={0.1}
                            className="w-full"
                          />
                        </div>
                      </div>
                    );
                  })()}

                  {/* Fill 심볼라이저 편집 */}
                  {symbolizer.kind === 'Fill' && (() => {
                    const fillSymbolizer = symbolizer as FillSymbolizer;
                    return (
                      <div className="space-y-3">
                        <div className="grid grid-cols-2 gap-3">
                          <div className="space-y-2">
                            <Label className="text-xs text-muted-foreground">채우기 색상</Label>
                            <Input
                              type="color"
                              value={fillSymbolizer.color}
                              onChange={(e) => updateRuleSymbolizer({ color: e.target.value })}
                              className="h-8 w-full cursor-pointer"
                            />
                          </div>
                          <div className="space-y-2">
                            <div className="flex items-center justify-between">
                              <Label className="text-xs text-muted-foreground">투명도</Label>
                              <Badge variant="outline" className="text-xs">
                                {Math.round(fillSymbolizer.fillOpacity * 100)}%
                              </Badge>
                            </div>
                            <Slider
                              value={[fillSymbolizer.fillOpacity]}
                              onValueChange={([value]) => updateRuleSymbolizer({ fillOpacity: value })}
                              min={0}
                              max={1}
                              step={0.1}
                              className="w-full"
                            />
                          </div>
                        </div>

                        {/* 윤곽선 */}
                        {fillSymbolizer.outlineColor && (
                          <div className="grid grid-cols-2 gap-3">
                            <div className="space-y-2">
                              <Label className="text-xs text-muted-foreground">윤곽선 색상</Label>
                              <Input
                                type="color"
                                value={fillSymbolizer.outlineColor}
                                onChange={(e) => updateRuleSymbolizer({ outlineColor: e.target.value })}
                                className="h-8 w-full cursor-pointer"
                              />
                            </div>
                            <div className="space-y-2">
                              <div className="flex items-center justify-between">
                                <Label className="text-xs text-muted-foreground">윤곽선 두께</Label>
                                <Badge variant="outline" className="text-xs">
                                  {fillSymbolizer.outlineWidth || 1}px
                                </Badge>
                              </div>
                              <Slider
                                value={[fillSymbolizer.outlineWidth || 1]}
                                onValueChange={([value]) => updateRuleSymbolizer({ outlineWidth: value })}
                                min={0}
                                max={10}
                                step={1}
                                className="w-full"
                              />
                            </div>
                          </div>
                        )}
                      </div>
                    );
                  })()}
                </CardContent>
              </Card>
            );
          })}
        </div>
      );
    }

    // 단일 rule인 경우 (기존 로직)
    const rule = style.rules[0];
    const symbolizer = rule.symbolizers[0];

    if (symbolizer.kind === 'Mark') {
      const markSymbolizer = symbolizer as MarkSymbolizer;
      return (
        <div className="space-y-4">
          {/* 심볼 타입 선택 */}
          <Card>
            <CardContent className="p-4">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Label className="text-sm font-medium">심볼 타입</Label>
                  <div className="flex items-center gap-2">
                    {getSymbolIcon(markSymbolizer.wellKnownName)}
                    <span className="text-xs text-muted-foreground capitalize">
                      {markSymbolizer.wellKnownName}
                    </span>
                  </div>
                </div>
                <Select
                  value={markSymbolizer.wellKnownName}
                  onValueChange={(value) => {
                    const newStyle: WMSLayerStyle = {
                      ...style,
                      rules: [{
                        ...rule,
                        symbolizers: [{
                          ...markSymbolizer,
                          wellKnownName: value as any
                        }]
                      }]
                    };
                    handleStyleChange(newStyle);
                  }}
                >
                  <SelectTrigger className="h-9">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="circle">
                      <div className="flex items-center gap-2">
                        <Circle size={14} />
                        <span>원</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="square">
                      <div className="flex items-center gap-2">
                        <Square size={14} />
                        <span>사각형</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="triangle">
                      <div className="flex items-center gap-2">
                        <Triangle size={14} />
                        <span>삼각형</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="star">
                      <div className="flex items-center gap-2">
                        <Star size={14} />
                        <span>별</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="cross">
                      <div className="flex items-center gap-2">
                        <Plus size={14} />
                        <span>십자</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="x">
                      <div className="flex items-center gap-2">
                        <X size={14} />
                        <span>X</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* 크기 설정 */}
          <Card>
            <CardContent className="p-4">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Label className="text-sm font-medium">크기</Label>
                  <Badge variant="secondary">{markSymbolizer.radius}px</Badge>
                </div>
                <Slider
                  value={[markSymbolizer.radius]}
                  onValueChange={([value]) => {
                    const newStyle: WMSLayerStyle = {
                      ...style,
                      rules: [{
                        ...rule,
                        symbolizers: [{
                          ...markSymbolizer,
                          radius: value
                        }]
                      }]
                    };
                    handleStyleChange(newStyle);
                  }}
                  min={1}
                  max={20}
                  step={1}
                  className="w-full"
                />
              </div>
            </CardContent>
          </Card>

          {/* 색상 및 투명도 설정 */}
          <Card>
            <CardContent className="p-4">
              <div className="space-y-4">
                <Label className="text-sm font-medium">채우기</Label>
                <div className="grid grid-cols-2 gap-3">
                  <div className="space-y-2">
                    <Label className="text-xs text-muted-foreground">색상</Label>
                    <div className="flex items-center gap-2">
                      <Input
                        type="color"
                        value={markSymbolizer.color}
                        onChange={(e) => {
                          const newStyle: WMSLayerStyle = {
                            ...style,
                            rules: [{
                              ...rule,
                              symbolizers: [{
                                ...markSymbolizer,
                                color: e.target.value
                              }]
                            }]
                          };
                          handleStyleChange(newStyle);
                        }}
                        className="h-9 w-full cursor-pointer"
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label className="text-xs text-muted-foreground">투명도</Label>
                      <Badge variant="outline" className="text-xs">
                        {Math.round(markSymbolizer.fillOpacity * 100)}%
                      </Badge>
                    </div>
                    <Slider
                      value={[markSymbolizer.fillOpacity]}
                      onValueChange={([value]) => {
                        const newStyle: WMSLayerStyle = {
                          ...style,
                          rules: [{
                            ...rule,
                            symbolizers: [{
                              ...markSymbolizer,
                              fillOpacity: value
                            }]
                          }]
                        };
                        handleStyleChange(newStyle);
                      }}
                      min={0}
                      max={1}
                      step={0.1}
                      className="w-full"
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 윤곽선 설정 */}
          {markSymbolizer.strokeColor && (
            <Card>
              <CardContent className="p-4">
                <div className="space-y-4">
                  <Label className="text-sm font-medium">윤곽선</Label>
                  <div className="grid grid-cols-2 gap-3">
                    <div className="space-y-2">
                      <Label className="text-xs text-muted-foreground">색상</Label>
                      <Input
                        type="color"
                        value={markSymbolizer.strokeColor}
                        onChange={(e) => {
                          const newStyle: WMSLayerStyle = {
                            ...style,
                            rules: [{
                              ...rule,
                              symbolizers: [{
                                ...markSymbolizer,
                                strokeColor: e.target.value
                              }]
                            }]
                          };
                          handleStyleChange(newStyle);
                        }}
                        className="h-9 w-full cursor-pointer"
                      />
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label className="text-xs text-muted-foreground">두께</Label>
                        <Badge variant="outline" className="text-xs">
                          {markSymbolizer.strokeWidth || 1}px
                        </Badge>
                      </div>
                      <Slider
                        value={[markSymbolizer.strokeWidth || 1]}
                        onValueChange={([value]) => {
                          const newStyle: WMSLayerStyle = {
                            ...style,
                            rules: [{
                              ...rule,
                              symbolizers: [{
                                ...markSymbolizer,
                                strokeWidth: value
                              }]
                            }]
                          };
                          handleStyleChange(newStyle);
                        }}
                        min={0}
                        max={10}
                        step={1}
                        className="w-full"
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      );
    }

    if (symbolizer.kind === 'Line') {
      const lineSymbolizer = symbolizer as LineSymbolizer;
      return (
        <div className="space-y-4">
          <Card>
            <CardContent className="p-4">
              <div className="space-y-4">
                <Label className="text-sm font-medium">선 스타일</Label>
                <div className="grid grid-cols-2 gap-3">
                  <div className="space-y-2">
                    <Label className="text-xs text-muted-foreground">색상</Label>
                    <Input
                      type="color"
                      value={lineSymbolizer.color}
                      onChange={(e) => {
                        const newStyle: WMSLayerStyle = {
                          ...style,
                          rules: [{
                            ...rule,
                            symbolizers: [{
                              ...lineSymbolizer,
                              color: e.target.value
                            }]
                          }]
                        };
                        handleStyleChange(newStyle);
                      }}
                      className="h-9 w-full cursor-pointer"
                    />
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label className="text-xs text-muted-foreground">투명도</Label>
                      <Badge variant="outline" className="text-xs">
                        {Math.round(lineSymbolizer.opacity * 100)}%
                      </Badge>
                    </div>
                    <Slider
                      value={[lineSymbolizer.opacity]}
                      onValueChange={([value]) => {
                        const newStyle: WMSLayerStyle = {
                          ...style,
                          rules: [{
                            ...rule,
                            symbolizers: [{
                              ...lineSymbolizer,
                              opacity: value
                            }]
                          }]
                        };
                        handleStyleChange(newStyle);
                      }}
                      min={0}
                      max={1}
                      step={0.1}
                      className="w-full"
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Label className="text-sm font-medium">선 두께</Label>
                  <Badge variant="secondary">{lineSymbolizer.width}px</Badge>
                </div>
                <Slider
                  value={[lineSymbolizer.width]}
                  onValueChange={([value]) => {
                    const newStyle: WMSLayerStyle = {
                      ...style,
                      rules: [{
                        ...rule,
                        symbolizers: [{
                          ...lineSymbolizer,
                          width: value
                        }]
                      }]
                    };
                    handleStyleChange(newStyle);
                  }}
                  min={1}
                  max={10}
                  step={1}
                  className="w-full"
                />
              </div>
            </CardContent>
          </Card>
        </div>
      );
    }

    if (symbolizer.kind === 'Fill') {
      const fillSymbolizer = symbolizer as FillSymbolizer;
      return (
        <div className="space-y-4">
          <Card>
            <CardContent className="p-4">
              <div className="space-y-4">
                <Label className="text-sm font-medium">채우기</Label>
                <div className="grid grid-cols-2 gap-3">
                  <div className="space-y-2">
                    <Label className="text-xs text-muted-foreground">색상</Label>
                    <Input
                      type="color"
                      value={fillSymbolizer.color}
                      onChange={(e) => {
                        const newStyle: WMSLayerStyle = {
                          ...style,
                          rules: [{
                            ...rule,
                            symbolizers: [{
                              ...fillSymbolizer,
                              color: e.target.value
                            }]
                          }]
                        };
                        handleStyleChange(newStyle);
                      }}
                      className="h-9 w-full cursor-pointer"
                    />
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label className="text-xs text-muted-foreground">투명도</Label>
                      <Badge variant="outline" className="text-xs">
                        {Math.round(fillSymbolizer.fillOpacity * 100)}%
                      </Badge>
                    </div>
                    <Slider
                      value={[fillSymbolizer.fillOpacity]}
                      onValueChange={([value]) => {
                        const newStyle: WMSLayerStyle = {
                          ...style,
                          rules: [{
                            ...rule,
                            symbolizers: [{
                              ...fillSymbolizer,
                              fillOpacity: value
                            }]
                          }]
                        };
                        handleStyleChange(newStyle);
                      }}
                      min={0}
                      max={1}
                      step={0.1}
                      className="w-full"
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {fillSymbolizer.outlineColor && (
            <Card>
              <CardContent className="p-4">
                <div className="space-y-4">
                  <Label className="text-sm font-medium">윤곽선</Label>
                  <div className="grid grid-cols-2 gap-3">
                    <div className="space-y-2">
                      <Label className="text-xs text-muted-foreground">색상</Label>
                      <Input
                        type="color"
                        value={fillSymbolizer.outlineColor}
                        onChange={(e) => {
                          const newStyle: WMSLayerStyle = {
                            ...style,
                            rules: [{
                              ...rule,
                              symbolizers: [{
                                ...fillSymbolizer,
                                outlineColor: e.target.value
                              }]
                            }]
                          };
                          handleStyleChange(newStyle);
                        }}
                        className="h-9 w-full cursor-pointer"
                      />
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label className="text-xs text-muted-foreground">두께</Label>
                        <Badge variant="outline" className="text-xs">
                          {fillSymbolizer.outlineWidth || 1}px
                        </Badge>
                      </div>
                      <Slider
                        value={[fillSymbolizer.outlineWidth || 1]}
                        onValueChange={([value]) => {
                          const newStyle: WMSLayerStyle = {
                            ...style,
                            rules: [{
                              ...rule,
                              symbolizers: [{
                                ...fillSymbolizer,
                                outlineWidth: value
                              }]
                            }]
                          };
                          handleStyleChange(newStyle);
                        }}
                        min={0}
                        max={10}
                        step={1}
                        className="w-full"
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      );
    }

    return null;
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button 
          variant="ghost" 
          size="icon"
        >
          <Palette size={16} />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-96 p-0" side="left" align="start">
        <div className="p-4">
          {/* 헤더 */}
          <div className="space-y-3 mb-4">
            <div className="flex items-center justify-between">
              <h4 className="font-semibold text-base">레이어 스타일</h4>
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="text-xs">
                  {isWMSLayer ? 'WMS' : isWFSLayer ? 'WFS' : '기타'}
                </Badge>
                <Badge variant="secondary" className="text-xs">
                  {getGeometryTypeText(geometryType)}
                </Badge>
              </div>
            </div>

            <div className="text-sm text-muted-foreground font-medium truncate">
              {(layer as any).name || layer.id}
            </div>

            {/* 스타일 변경 버튼들 */}
            {isWMSLayer && (
              <div className="space-y-2">
                {/* 현재 스타일이 단일인 경우 */}
                {('rules' in currentStyle && currentStyle.rules.length === 1) && (
                  <div className="flex gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="flex-1 text-xs text-muted-foreground"
                      onClick={handleResetStyle}
                    >
                      초기화
                    </Button>
                  </div>
                )}

                {/* 현재 스타일이 유형별인 경우 */}
                {('rules' in currentStyle && currentStyle.rules.length > 1) && (
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1 text-xs"
                      onClick={handleConvertToSingleStyle}
                    >
                      <Circle size={14} className="mr-1" />
                      단일 스타일로 변경
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="flex-1 text-xs text-muted-foreground"
                      onClick={handleResetStyle}
                    >
                      초기화
                    </Button>
                  </div>
                )}
              </div>
            )}

            <Separator />
          </div>

          {/* 스타일 편집기 */}
          <div className="space-y-4">
            {isWMSLayer && 'rules' in currentStyle && (
              <div className="max-h-96 overflow-y-auto space-y-1">
                {renderWMSStyleEditor(currentStyle)}
              </div>
            )}

            {isWFSLayer && !('rules' in currentStyle) && (
              <Card>
                <CardContent className="p-6">
                  <div className="text-center space-y-2">
                    <div className="text-sm font-medium">개발 중</div>
                    <div className="text-xs text-muted-foreground">
                      WFS 레이어 스타일 편집기는 추후 구현 예정입니다.
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {!isWMSLayer && !isWFSLayer && (
              <Card>
                <CardContent className="p-6">
                  <div className="text-center space-y-2">
                    <div className="text-sm font-medium">지원되지 않음</div>
                    <div className="text-xs text-muted-foreground">
                      이 레이어 타입은 스타일 편집을 지원하지 않습니다.
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}