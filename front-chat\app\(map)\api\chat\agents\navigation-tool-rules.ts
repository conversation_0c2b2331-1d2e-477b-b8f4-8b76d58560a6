/**
 * Navigation 에이전트 도구 사용 규칙
 * 위치 검색, 경로 탐색, 지도 이동 관련 체계적 규칙 정의
 */

// 핵심 공통 규칙
export const CORE_NAVIGATION_RULES = {
  // 작업 순서 (최우선)
  WORK_ORDER: "의도분석 메시지 확인 → 필요한 도구 순차 호출 → 결과 확인 → 사용자에게 명확한 피드백",
  
  // 좌표 형식 통일
  COORDINATE_FORMAT: "모든 좌표는 'longitude,latitude' 형식으로 통일 (예: '126.123,37.456')",
  
  // 에러 처리
  ERROR_HANDLING: "검색 실패 시 키워드 변경하여 재시도, 그래도 실패하면 사용자에게 명확한 안내",
  
  // HIL 도구 사용
  HIL_USAGE: "검색 결과 여러개: chooseOption 필수, 검색 실패: getUserInput, 중요 작업: confirmWithCheckbox",
};

// 도구별 특화 규칙
export const NAVIGATION_TOOL_RULES = {
  searchAddress: `
    - 용도: 주소, 건물명, 시설명 검색 (가장 기본적인 위치 검색)
    - 파라미터: keyword(필수), showMultipleResults=false, targetSrid=4326
    - 🚨 중요: 자동으로 첫 번째 검색 결과로 지도 이동됨 (별도 이동 도구 불필요)
    - 성공 시: 좌표 정보 추출하여 사용자에게 제공
    - 실패 시: searchDestination으로 재시도`,
    
  searchDestination: `
    - 용도: searchAddress 실패 시 대안, 목적지 검색
    - 파라미터: keyword(필수), showMultipleResults=false
    - 사용 시점: searchAddress에서 결과가 없거나 부정확할 때`,
    
  searchOrigin: `
    - 용도: 출발지 검색 (경로 탐색 시 출발지 지정)
    - 파라미터: keyword(필수), showMultipleResults=false
    - 사용 시점: 사용자가 특정 출발지를 지정했을 때`,
    
  getLocation: `
    - 용도: 현재 위치 확인
    - 파라미터: location="현재 위치"
    - 사용 시점: 경로 탐색 시 출발지가 "현재 위치", "여기서", "현위치" 등의 키워드가 감지된 경우일 때`,
    
  searchDirections: `
    - 용도: 경로 탐색 및 길찾기
    - 파라미터: origin(필수), destination(필수), priority="RECOMMEND"
    - 좌표 형식: "longitude,latitude" 통일 필수
    - 전제 조건: origin과 destination 좌표가 모두 확보된 상태`,
    
  moveMapByDirection: `
    - 용도: 지도 이동 (찾은 위치로 지도 중심 이동)
    - 파라미터: direction, distance
    - 사용 시점: 위치 검색 완료 후 해당 위치로 지도 이동`,
} as const;

// 상세 워크플로우
export const NAVIGATION_WORKFLOWS = {
  LOCATION_SEARCH: `
    1. searchAddress로 키워드 검색 (자동으로 첫 번째 결과로 지도 이동됨)
    2. 검색된 위치 정보를 사용자에게 명확히 제공
    3. 🚨 주의: 추가 searchAddress 호출이나 지도 이동 도구 사용 금지`,
    
  ROUTE_SEARCH: `
    1. 목적지 검색 (searchAddress 또는 searchDestination)
    2. 출발지 확인: "현재 위치"면 getLocation 호출, 특정 장소면 searchOrigin
    3. 좌표 형식 통일 확인 (longitude,latitude)
    4. searchDirections로 경로 탐색
    5. 경로 정보를 사용자에게 제공`,
} as const;

// 상세화된 프롬프트 생성 함수
export function createNavigationToolRules(): string {
  return `
**🚨 Navigation 도구 사용 핵심 규칙:**

**최우선 원칙:**
- ${CORE_NAVIGATION_RULES.WORK_ORDER}
- ${CORE_NAVIGATION_RULES.COORDINATE_FORMAT}
- ${CORE_NAVIGATION_RULES.ERROR_HANDLING}

**도구별 핵심 규칙:**
- **searchAddress**: ${NAVIGATION_TOOL_RULES.searchAddress}
- **searchDestination**: ${NAVIGATION_TOOL_RULES.searchDestination}
- **searchOrigin**: ${NAVIGATION_TOOL_RULES.searchOrigin}
- **getLocation**: ${NAVIGATION_TOOL_RULES.getLocation}
- **searchDirections**: ${NAVIGATION_TOOL_RULES.searchDirections}

**🎯 상세 작업 흐름 (필수 준수):**

**위치 검색 및 지도 이동**: ${NAVIGATION_WORKFLOWS.LOCATION_SEARCH}

**경로 탐색**: ${NAVIGATION_WORKFLOWS.ROUTE_SEARCH}

**HIL 도구 활용:**
- ${CORE_NAVIGATION_RULES.HIL_USAGE}

**에러 방지 체크리스트:**
- 의도분석 메시지의 작업 단계를 확인했는가?
- 좌표 형식이 'longitude,latitude'로 통일되었는가?
- 검색 실패 시 대안 도구를 시도했는가?
- 경로 탐색 전에 출발지와 목적지 좌표를 모두 확보했는가?
- 여러 검색 결과 시 chooseOption을 사용했는가?
- 사용자에게 명확한 결과를 제공했는가?
  `.trim();
}

// Navigation 에이전트별 맞춤 규칙 생성 함수
export function getNavigationToolRulesForAgent(): string {
  const baseRules = createNavigationToolRules();
  
  return baseRules + `

**🎯 Navigation 에이전트 특화 규칙:**

**핵심 책임:**
- 위치 검색 및 좌표 정보 제공
- 경로 탐색 및 길찾기 서비스
- 지도 이동 및 네비게이션 지원

**성공 기준:**
- 요청된 위치의 정확한 좌표 제공
- 경로 탐색 시 실행 가능한 길찾기 정보 제공
- 검색 실패 시에도 명확한 대안 제시

**응답 패턴:**
- 위치 검색: "~의 위치를 찾았습니다. 주소는 ~이고, 좌표는 ~입니다."
- 경로 탐색: "~에서 ~까지의 경로를 찾았습니다. 거리는 ~, 예상 시간은 ~입니다."
- 검색 실패: "~을 찾지 못했습니다. 다른 키워드로 검색하거나 구체적인 주소를 알려주세요."
  `.trim();
}
