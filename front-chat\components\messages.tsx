"use client";

import { ChatRequestOptions, Message } from 'ai';
import { useScrollToBottom } from '@/lib/hooks/use-scroll-to-bottom';
import { Dispatch, memo, SetStateAction } from 'react';
import equal from 'fast-deep-equal';
import { Message as PreviewMessage, ThinkingMessage } from './message';
import { Overview } from '@/components/chat-map/overview';
import { Vote } from '@/lib/db/schema';
import { UseMapReturn } from "@geon-map/odf";


interface MessagesProps {
  chatId: string;
  // block: UIBlock;
  // setBlock: Dispatch<SetStateAction<UIBlock>>;
  votes: Array<Vote> | undefined;
  messages: Array<Message>;
  setMessages: (messages: Message[] | ((messages: Message[]) => Message[])) => void;
  reload: (chatRequestOptions?: ChatRequestOptions) => Promise<string | null | undefined>;
  isReadonly: boolean;
  setInput: (input: string) => void;  // Add this line
  status: 'submitted' | 'streaming' | 'ready' | 'error';
  error: Error | undefined | null; // error prop 추가
  addToolResult: ({
    toolCallId,
    result,
  }: {
    toolCallId: string;
    result: string;
  }) => void;
  mapState?: UseMapReturn; // mapState 추가
}

function PureMessages({
  chatId,
  // block,
  // setBlock,
  votes,
  messages,
  setMessages,
  reload,
  isReadonly,
  setInput,  // Add this line
  status,
  error, // error prop 추가
  addToolResult,
  mapState, // mapState 추가
}: MessagesProps) {
  const [messagesContainerRef, messagesEndRef] = useScrollToBottom<HTMLDivElement>();
  return (
    <div
      ref={messagesContainerRef}
      className="flex flex-col min-w-0 gap-12 flex-1 overflow-y-auto pt-4 styled-scrollbar"
    >
      {messages.length === 0 && <Overview setInput={setInput} />}

      {messages.map((message) => (
        <PreviewMessage
          key={message.id}
          chatId={chatId}
          message={message}
          status={status} // status prop 전달
          vote={
            votes
              ? votes.find((vote) => vote.messageId === message.id)
              : undefined
          }
          setMessages={setMessages}
          reload={reload}
          isReadonly={isReadonly}
          addToolResult={addToolResult}
          mapState={mapState} // mapState 전달
        />

      ))}

      {/* ThinkingMessage: 마지막 메시지가 사용자 입력이고, 현재 제출/스트리밍 중일 때 표시 */}
      {(status === 'submitted') && 
        <ThinkingMessage />}

      {/* Status and Error Messages */}
        {status === 'error' && error && (
        <div className="px-4 py-2 text-center text-sm">
          <p className="text-red-500">
            오류가 발생했습니다. 
            지속적으로 발생되는 경우 새 대화를 시도해주세요. 
            {error.message}
          </p>
        </div>
      )}

      <div
        ref={messagesEndRef}
        className="shrink-0 min-w-[24px] min-h-[24px]"
      />
    </div>
  );
}


export const Messages = memo(PureMessages, (prevProps, nextProps) => {
  if (prevProps.status !== nextProps.status) return false;
  if (prevProps.status && nextProps.status) return false;
  if (prevProps.messages.length !== nextProps.messages.length) return false;
  if (!equal(prevProps.messages, nextProps.messages)) return false;
  if (!equal(prevProps.votes, nextProps.votes)) return false;

  return true;
});