"use client";

import React from "react";
import { Map, Check, Globe2, Mountain, Image, Paintbrush } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { UseMapReturn } from "@geon-map/odf";
import { CompactResultTrigger } from "./common/compact-result-trigger";
import { getToolDisplayInfo } from "../annotations";


interface BasemapChangeResponse {
  basemap?: string;
  basemapId?: string;
  basemapName?: string;
  error?: string;
}

interface BasemapChangeResultProps {
  content: BasemapChangeResponse | string;
  className?: string;
  mapState?: UseMapReturn;
}



// 배경지도 정보 매핑
const basemapInfo = {
  eMapBasic: {
    name: "일반지도",
    icon: Globe2,
    color: "#4C6EF5",
  },
  eMapAIR: {
    name: "항공지도",
    icon: Image,
    color: "#40C057",
  },
  eMapColor: {
    name: "색각지도",
    icon: Paintbrush,
    color: "#FA5252",
  },
  eMapWhite: {
    name: "백지도",
    icon: Mountain,
    color: "#845EF7",
  }
} as const;

type BasemapId = keyof typeof basemapInfo;

const getBasemapDisplayName = (basemapId: string): string => {
  return basemapInfo[basemapId as BasemapId]?.name || basemapId;
};

const getBasemapIcon = (basemapId: string) => {
  return basemapInfo[basemapId as BasemapId]?.icon || Map;
};

const getBasemapColor = (basemapId: string): string => {
  return basemapInfo[basemapId as BasemapId]?.color || "#4C6EF5";
};

export function BasemapChangeResult({ content, className, mapState }: BasemapChangeResultProps) {
  let result: BasemapChangeResponse;

  try {
    result = typeof content === 'string' ? JSON.parse(content) : content;
  } catch (e) {
    result = { error: 'Invalid content format' };
  }

  const toolInfo = getToolDisplayInfo("changeBasemap");

  // early return은 Hook 호출 후에
  if (result.error) {
    return (
      <CompactResultTrigger
        icon={toolInfo.icon}
        title={toolInfo.label}
        state="partial-call"
        className={className}
        titleExtra={
          <Badge variant="outline" className="text-xs py-0 border bg-red-100/80 text-red-700 border-red-200/60">
            오류
          </Badge>
        }
      >
        <div className="text-xs text-red-700">
          {result.error}
        </div>
      </CompactResultTrigger>
    );
  }



  const basemapId = result.basemap || result.basemapId || 'eMapBasic';
  const displayName = result.basemapName || getBasemapDisplayName(basemapId);
  const basemapIcon = getBasemapIcon(basemapId);
  const iconColor = getBasemapColor(basemapId);

  return (
    <CompactResultTrigger
      icon={toolInfo.icon}
      title={toolInfo.label}
      state="result"
      className={className}
      titleExtra={
        <div className="flex items-center gap-1">
          <Badge variant="outline" className="text-xs py-0 border bg-emerald-100/80 text-emerald-700 border-emerald-200/60">
            {displayName}
          </Badge>
          <div
            className="flex h-4 w-4 items-center justify-center rounded-sm"
            style={{ backgroundColor: `${iconColor}20` }}
          >
            {React.createElement(basemapIcon, {
              className: "h-2.5 w-2.5",
              style: { color: iconColor }
            })}
          </div>
        </div>
      }
    >
      <div className="text-xs text-neutral-600">
        배경지도가 <span className="font-medium">{displayName}</span>로 변경되었습니다.
      </div>
    </CompactResultTrigger>
  );
}
