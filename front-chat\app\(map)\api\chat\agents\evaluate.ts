import { openai } from "@ai-sdk/openai";
import { generateObject } from "ai";
import { z } from "zod";

// 평가 결과 스키마 (단순화)
export const evaluationSchema = z.object({
  isCompleted: z.boolean().describe("사용자의 의도가 충분히 해결되었는지 (도구 호출 여부와 무관)"),
  reason: z.string().describe("완료/미완료 판단 이유"),
  improvementSuggestions: z.array(z.string()).default([]).describe("미완료 시 개선 제안사항"),
});

export type EvaluationResult = z.infer<typeof evaluationSchema>;

// 평가자 함수
export async function evaluateAgentResult(
  intentMessage: string,
  agentResponse: string,
  toolCalls: any[]
): Promise<EvaluationResult> {
  try {
    // HIL 도구 호출 확인 (최우선 처리)
    const hilTools = ['chooseOption', 'getUserInput', 'confirmWithCheckbox', 'getLocation'];
    const hasHILTool = toolCalls.some(call => hilTools.includes(call.toolName));

    if (hasHILTool) {
      return {
        isCompleted: true,
        reason: "사용자와의 상호작용을 진행 중이므로 완료로 판단합니다.",
        improvementSuggestions: [],
      };
    }
    const system = `당신은 AI Agent의 작업 수행 결과를 평가하는 전문가입니다.

      **🚨 중요: 이 평가는 Agent 간 내부 통신용입니다 🚨**
      - reason과 improvementSuggestions는 다음 Agent가 참고할 기술적 지침입니다
      - 다음 작업자(Agent)가 이해할 수 있는 구체적이고 실행 가능한 지시사항을 제공하세요

      **핵심 평가 원칙:**
      1. 도구 호출 여부와 관계없이 사용자의 의도가 충분히 해결되었는지 판단
      2. "안녕하세요" 같은 간단한 인사는 도구 호출 없이도 완료될 수 있음
      3. 단순하게 "완료" vs "미완료" 두 가지로만 구분

      **완료 판단 기준 (isCompleted: true):**
      - 사용자의 원래 요청이 충분히 해결됨
      - 의도분석에서 제시한 목표가 달성됨
      - 더 이상 추가 작업이 필요하지 않음

      **미완료 판단 기준 (isCompleted: false):**
      - 사용자 요청이 아직 해결되지 않음
      - 필요한 작업이 수행되지 않음
      - 도구 호출 실패나 에러 발생
      - "분석을 시작하겠습니다" 등의 예고만 하고 실제 작업 미수행

      **🚨 도구 사용 규칙 (에러 진단 시 필수 확인):**

      **getLayerAttributes 도구:**
      - 필수 전제조건: getLayer 도구를 먼저 호출하여 lyrId, namespace, cntntsId 정보 확보
      - 필수 파라미터: lyrId (getLayerList 결과), namespace (getLayer 결과), cntntsId (getLayer 결과)
      - 일반적 에러: 파라미터 누락 시 '400 Bad Request' 에러 발생
      - 해결방법: getLayer 도구 결과에서 정확한 파라미터 값을 추출하여 사용

      **generateCategoricalStyle 도구:**
      - 필수 전제조건: getLayerAttributes로 컬럼 정보를 먼저 확보
      - 필수 정보: 스타일링할 속성 컬럼명과 데이터 타입 확인
      - 올바른 워크플로우: 레이어 추가 → 속성 조회 → 컬럼 식별 → 카테고리별 스타일 적용

      **일반적인 워크플로우:**
      - 카테고리별 스타일링: getLayerList → getLayer → getLayerAttributes → generateCategoricalStyle
      - 복합 필터링: getLayerList → getLayer → getLayerAttributes → createLayerFilter → updateLayerStyle

      **평가 응답 작성 가이드 (Agent 간 내부 통신):**

      **reason 작성 원칙:**
      - 구체적인 기술적 근거: 어떤 도구를 호출했는지, 에러 코드와 메시지 명시
      - 워크플로우 분석: 필요한 전제조건이 충족되었는지 확인
      - 파라미터 검증: 도구 호출 시 올바른 파라미터가 사용되었는지 분석

      **improvementSuggestions 작성 원칙:**
      - 구체적 기술적 해결방안: "getLayer 결과에서 namespace와 cntntsId를 추출하여 getLayerAttributes에 전달하세요"
      - 올바른 워크플로우 제시: "먼저 getLayer로 레이어를 추가한 후 getLayerAttributes를 호출하세요"
      - 파라미터 수정 방법: "lyrId는 getLayerList 결과에서, namespace와 cntntsId는 getLayer 결과에서 가져오세요"
      - 대안 방법: "속성 조회 실패 시 다른 레이어를 선택하거나 사용자에게 확인을 요청하세요"

      **판단 가이드라인:**
      - 전체 맥락을 종합적으로 고려하여 판단
      - 특정 키워드나 패턴에만 의존하지 말 것
      - 사용자 관점에서 요청이 해결되었는지 중점 평가
      `
      ;

    const prompt = `다음 Agent 수행 결과를 평가하세요:

**의도분석 메시지:**
${intentMessage}

**Agent 응답:**
${agentResponse}

**도구 정보:**
${toolCalls.length > 0 ? 
  toolCalls.map((call) => {
    return `toolName: ${call.toolName}, args: ${JSON.stringify(call.args)}`;
  }).join("\n")
  : "도구 호출 없음"}

**도구 호출 결과:**
${
  toolCalls.length > 0
    ? toolCalls
        .map((call, index) => {
          // prunedResult가 있으면 그것을 사용, 없으면 원본 result 사용
          const resultToShow = call.prunedResult || call.result;
          return `도구 ${index + 1}: ${call.toolName}
파라미터: ${JSON.stringify(call.args, null, 2)}
결과: ${JSON.stringify(resultToShow, null, 2)}`;
        })
        .join("\n\n")
    : "도구 호출 없음"
}

**종합 평가 요청:**

**평가 기준**
1. **의도분석 목표 달성도**: 요청된 작업이 실제로 완료되었는가?
2. **작업 진행 상태 파악**:
   - 준비만 하고 핵심 작업 미수행 → isCompleted: false
   - 예: "분석을 시작하겠습니다" 후 performDensityAnalysis 미호출
   - 예: "스타일을 변경하겠습니다" 후 updateLayerStyle 미호출
3. **에러 및 실패 상황**: 도구 호출 실패나 잘못된 사용 → isCompleted: false

**🚨 핵심 판단 원칙:**
- **HIL 도구 호출 시**: 무조건 isCompleted: true (최우선)
- **작업 예고 후 미수행**: isCompleted: false
- **실제 작업 완료**: isCompleted: true
- **에러 발생**: isCompleted: false

**평가 응답 작성 가이드:**

**reason 작성 원칙:**
- 구체적인 근거 제시: 어떤 도구를 호출했는지, 결과가 어땠는지 명시
- 사용자 요청과 연결: 원래 요청이 무엇이었고 해결되었는지/실패했는지 설명
- 판단 논리 설명: 왜 완료/미완료로 판단했는지 논리적 근거 제시

**improvementSuggestions 작성 원칙:**
- 구체적이고 실행 가능한 제안: "~를 확인하세요" 보다는 "~도구로 ~를 다시 시도하세요"
- 기술적 세부사항 포함: 파라미터 형식, 도구 사용법, 대안 방법 등
- 우선순위 순서: 가장 중요한 개선사항부터 나열
- 최소 2-4개의 구체적 제안 제공

**예시 템플릿:**
reason: "[도구명] 도구를 호출했으나 [구체적 에러/결과]로 인해 사용자의 [원래 요청]이 [성공/실패]했습니다. [판단 근거]"
improvementSuggestions: ["구체적 기술적 해결방안", "대안 도구 사용법", "파라미터 수정 방법", "추가 확인 사항"]
`;

    const { object: evaluation } = await generateObject({
      // 평가자, 의도분석(planner) 는 가장 진보된 모델, executor는 lite한 모델로 설정해야
      // 성능 및 요금 측면에서 최적의 결과를 얻는 듯함.
      model: openai("gpt-4.1-mini"),
      schema: evaluationSchema,
      system,
      prompt,
      temperature: 0
    });

    console.log("평가 결과:", evaluation);

    return evaluation;
  } catch (error) {
    console.error("평가자 실행 실패:", error);

    // 평가자 실패 시 기본값 반환 (미완료로 처리하여 재시도)
    return {
      isCompleted: false,
      reason: "평가자 오류로 인한 기본 판단: 작업이 미완료된 것으로 처리",
      improvementSuggestions: [
        "의도분석 메시지에 따라 필요한 도구를 호출하세요",
        "구체적인 작업을 수행하여 사용자의 요청을 완료하세요"
      ],
    };
  }
}

// 개선 메시지 생성
export function createImprovementMessage(evaluation: EvaluationResult): string {
  const suggestions = evaluation.improvementSuggestions || ["작업을 완료하기 위해 필요한 도구를 호출하세요"];
  const suggestionText = suggestions.join("\n- ");

  return `🔄 이전 시도가 불완전했습니다. 다음 사항을 개선하여 작업을 완료하세요:

**미완료 이유:**
${evaluation.reason}

**개선사항:**
- ${suggestionText}

**중요:** 설명보다는 실제 도구 호출을 통해 작업을 수행하세요.`;
}
