"use client";

import React from "react";
import { Zap, MapPin, Download } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { toast } from "sonner";
import { CompactResultTrigger } from "./common/compact-result-trigger";
import { getToolDisplayInfo } from "../annotations";

// GeoJSON FeatureCollection 구조에 맞춘 간소화된 인터페이스
interface DensityAnalysisResponse {
  type?: string; // 'FeatureCollection'
  features?: Array<any>; // GeoJSON features 배열
  layerName?: string;
  status?: string;
  error?: string;
}

interface DensityAnalysisResultProps {
  content: DensityAnalysisResponse | string;
  className?: string;
}

export function DensityAnalysisResult({ content, className }: DensityAnalysisResultProps) {
  let result: DensityAnalysisResponse;

  try {
    result = typeof content === 'string' ? JSON.parse(content) : content;
  } catch (e) {
    return null;
  }

  // GeoJSON FeatureCollection에서 피처 개수 추출
  const featureCount = result.features?.length || 0;

  const handleDownloadResult = () => {
    if (!featureCount) {
      toast.error("다운로드할 분석 결과가 없습니다");
      return;
    }

    try {
      const dataStr = JSON.stringify(result, null, 2);
      const dataUri = 'data:application/json;charset=utf-8,' + encodeURIComponent(dataStr);
      const exportFileDefaultName = `density_analysis_${new Date().toISOString().split('T')[0]}.json`;

      const linkElement = document.createElement('a');
      linkElement.setAttribute('href', dataUri);
      linkElement.setAttribute('download', exportFileDefaultName);
      linkElement.click();

      toast.success("분석 결과를 다운로드했습니다");
    } catch (error) {
      toast.error("다운로드 중 오류가 발생했습니다");
    }
  };

  if (result.error || result.status === 'error') {
    return (
      <div className={cn("p-4 rounded-lg border border-red-200 bg-red-50", className)}>
        <div className="flex items-center gap-3">
          <div className="p-2 rounded-full bg-red-100 text-red-600">
            <Zap className="h-4 w-4" />
          </div>
          <div>
            <p className="font-medium text-red-900">밀도 분석을 완료할 수 없습니다</p>
            <p className="text-sm text-red-700">
              {result.error || "레이어 데이터에 문제가 있거나 분석 조건이 올바르지 않습니다"}
            </p>
          </div>
        </div>
      </div>
    );
  }

  const toolInfo = getToolDisplayInfo("performDensityAnalysis");

  return (
    <CompactResultTrigger
      icon={toolInfo.icon}
      title={toolInfo.label}
      state="result"
      className={className}
      titleExtra={
        <span className="text-xs text-neutral-600">
          {featureCount.toLocaleString()}개 데이터
        </span>
      }
    >
      <div className="space-y-3">
        <div className="text-xs text-neutral-600">
          총 <span className="font-medium">{featureCount.toLocaleString()}개</span> 데이터 분석이 완료되었습니다.
        </div>

        {/* 다운로드 버튼 */}
        <div className="flex justify-center">
          <Button
            variant="outline"
            size="sm"
            onClick={handleDownloadResult}
            className="h-6 px-3 text-xs flex items-center gap-1"
          >
            <Download className="h-3 w-3" />
            결과 다운로드
          </Button>
        </div>
      </div>
    </CompactResultTrigger>
  );
}
