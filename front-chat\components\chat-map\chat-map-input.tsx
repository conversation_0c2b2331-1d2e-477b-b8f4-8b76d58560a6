'use client';

import * as React from 'react';
import {
  useState,
  useRef,
  useEffect,
  useCallback,
  type Dispatch,
  type SetStateAction,
  type ChangeEvent,
  memo,
} from 'react';
import { useRouter } from 'next/navigation';
import { useLocalStorage, useWindowSize } from 'usehooks-ts';

import { toast } from 'sonner';
import equal from 'fast-deep-equal';
import {
  Attachment,
  ChatRequestOptions,
  CreateMessage,
  Message,
} from 'ai';

import {
  BetterTooltip,
} from '@/components/ui/tooltip';
import { Button } from '@/components/ui/button';
import { IconSpinner, StopIcon } from '@/components/ui/icons';
import { ForwardIcon, PaperclipIcon, PlusIcon, BrainIcon, SearchIcon, NavigationIcon } from 'lucide-react';
import { PreviewAttachment } from '@/components/preview-attachment';
import { useSidebar } from '../ui/sidebar';
import { SuggestedActions } from './suggested-actions';
import { Textarea } from '../ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { Badge } from '@/components/ui/badge';
import { HoverCard, HoverCardContent, HoverCardTrigger } from '@/components/ui/hover-card';
import { SmartNavigationHoverCard } from '@/components/ui/magic-hover-card';
import { AIReasoningCard } from '@/components/ui/ai-reasoning-card';
import { getReasoningDisabledMessage } from '@/lib/ai/models';
import { chat } from '@/lib/db/schema';

function PureChatMapInput({
  chatId,
  input,
  setInput,
  status,
  stop,
  attachments,
  setAttachments,
  messages,
  setMessages,
  append,
  handleSubmit,
  enableThinking,
  setEnableThinking,
  enableSmartNavigation,
  setEnableSmartNavigation,
  modelSupportsReasoning,
  selectedModelId,
}: {
  chatId: string;
  input: string;
  setInput: (value: string) => void;
  status: 'submitted' | 'streaming' | 'ready' | 'error';
  stop: () => void;
  attachments: Array<Attachment>;
  setAttachments: Dispatch<SetStateAction<Array<Attachment>>>;
  messages: Array<Message>;
  setMessages: Dispatch<SetStateAction<Array<Message>>>;
  append: (
    message: Message | CreateMessage,
    chatRequestOptions?: ChatRequestOptions,
  ) => Promise<string | null | undefined>;
  handleSubmit: (
    event?: {
      preventDefault?: () => void;
    },
    chatRequestOptions?: ChatRequestOptions,
  ) => void;
  enableThinking: boolean;
  setEnableThinking: React.Dispatch<React.SetStateAction<boolean>>;
  enableSmartNavigation: boolean;
  setEnableSmartNavigation: React.Dispatch<React.SetStateAction<boolean>>;
  modelSupportsReasoning: boolean;
  selectedModelId: string;
}) {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const router = useRouter();
  const { setOpenMobile } = useSidebar();
  const { width } = useWindowSize();
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const [uploadQueue, setUploadQueue] = useState<Array<string>>([]);

  const [localStorageInput, setLocalStorageInput] = useLocalStorage('input', '');

  const adjustHeight = () => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight + 2}px`;
    }
  };


  useEffect(() => {
    // if (textareaRef.current) {
    // adjustHeight();
    // }
  }, []);

  useEffect(() => {
    if (textareaRef.current) {
      const domValue = textareaRef.current.value;
      const finalValue = domValue || localStorageInput || '';
      setInput(finalValue);
      // adjustHeight();
    }
  }, []);

  useEffect(() => {
    setLocalStorageInput(input);
  }, [input, setLocalStorageInput]);


  const handleInput = useCallback((event: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInput(event.target.value);
    adjustHeight();
  }, [setInput]);

  const submitForm = useCallback(() => {
    window.history.replaceState({}, '', `/geon-2d-map/${chatId}`);

    handleSubmit(undefined, {
      experimental_attachments: attachments,
    });

    setAttachments([]);
    setLocalStorageInput('');

    if (width && width > 768) {
      textareaRef.current?.focus();
    }
  }, [
    attachments,
    handleSubmit,
    setAttachments,
    setLocalStorageInput,
    width,
    chatId,
  ]);

  const uploadFile = async (file: File) => {
    const formData = new FormData();
    formData.append('file', file);

    try {
      const response = await fetch('/api/files/upload', {
        method: 'POST',
        body: formData,
      });

      if (response.ok) {
        const data = await response.json();
        const { url, pathname, contentType } = data;
        return { url, name: pathname, contentType };
      }
      const { error } = await response.json();
      toast.error(error);
    } catch (error) {
      toast.error('Failed to upload file, please try again!');
    }
  };

  const handleFileChange = useCallback(
    async (event: ChangeEvent<HTMLInputElement>) => {
      const files = Array.from(event.target.files || []);
      setUploadQueue(files.map((file) => file.name));

      try {
        const uploadPromises = files.map((file) => uploadFile(file));
        const uploadedAttachments = await Promise.all(uploadPromises);
        const successfullyUploadedAttachments = uploadedAttachments.filter(
          (attachment) => attachment !== undefined,
        );

        setAttachments((currentAttachments) => [
          ...currentAttachments,
          ...successfullyUploadedAttachments,
        ]);
      } catch (error) {
        console.error('Error uploading files!', error);
      } finally {
        setUploadQueue([]);
      }
    },
    [setAttachments],
  );

  const removeFile = useCallback((url: string) => {
    setAttachments((prevFiles) => prevFiles.filter((value) => value.url !== url));
  }, [setAttachments]);

  return (
    <div className="relative w-full flex flex-1 flex-col gap-4">
      {/* {messages.length === 0 && attachments.length === 0 && uploadQueue.length === 0 && (
        <SuggestedActions setInput={setInput} append={append} chatId={chatId} />
      )} */}

      <input
        type="file"
        className="fixed -top-4 -left-4 size-0.5 opacity-0 pointer-events-none"
        ref={fileInputRef}
        multiple
        onChange={handleFileChange}
        tabIndex={-1}
      />

      {(attachments.length > 0 || uploadQueue.length > 0) && (
        <div className="flex flex-wrap gap-2 p-2 bg-background rounded-lg">
          {attachments.map((attachment) => (
            <PreviewAttachment
              key={attachment.url}
              attachment={attachment}
              removeFile={removeFile}
            />
          ))}

          {uploadQueue.map((filename) => (
            <PreviewAttachment
              key={filename}
              attachment={{
                url: '',
                name: filename,
                contentType: '',
              }}
              isUploading={true}
            />
          ))}
        </div>
      )}

      <div className="flex flex-col rounded-2xl border bg-background shadow-sm">
        <div className="relative">
          <Textarea
            ref={textareaRef}
            placeholder="원하는 지역, 레이어 등을 입력해주세요."
            value={input}
            onChange={handleInput}
            className="min-h-[48px] max-h-[40vh] overflow-hidden resize-none rounded-2xl !text-sm p-4 pr-16 w-full border-0 focus-visible:ring-0 focus-visible:ring-offset-0"
            rows={1}
            onKeyDown={(event) => {
              if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                if (status === 'submitted' || status === 'streaming') {
                  toast.error('이전 응답이 완료될 때까지 잠시만 기다려 주세요!');
                } else {
                  submitForm();
                }
              }
            }}
          />
          {/* Send Button - positioned absolutely in the input */}
          <div className="absolute right-2 top-1/2 -translate-y-1/2">
            {(status === 'submitted' || status === 'streaming') ? (
              <Button
                size="icon"
                variant="ghost"
                className="h-8 w-8 rounded-full"
                type='button'
                onClick={(event) => {
                  event.preventDefault();
                  stop();
                }}
              >
                <StopIcon size={16} />
              </Button>
            ) : (
              <Button
                size="icon"
                className="h-8 w-8 rounded-full"
                onClick={(event) => {
                  submitForm();
                }}
                disabled={input.length === 0 || uploadQueue.length > 0}
              >
                <ForwardIcon size={16} />
              </Button>
            )}
          </div>
        </div>

        <ActionButtons
          status={status}
          fileInputRef={fileInputRef}
          enableThinking={enableThinking}
          setEnableThinking={setEnableThinking}
          enableSmartNavigation={enableSmartNavigation}
          setEnableSmartNavigation={setEnableSmartNavigation}
          modelSupportsReasoning={modelSupportsReasoning}
          selectedModelId={selectedModelId}
        />
      </div>
    </div>
  );
}

const ActionButtons = memo(
  ({
    status,
    fileInputRef,
    enableThinking,
    setEnableThinking,
    enableSmartNavigation,
    setEnableSmartNavigation,
    modelSupportsReasoning,
    selectedModelId,
  }: {
    status: 'submitted' | 'streaming' | 'ready' | 'error';
    fileInputRef: React.RefObject<HTMLInputElement | null>;
    enableThinking: boolean;
    setEnableThinking: React.Dispatch<React.SetStateAction<boolean>>;
    enableSmartNavigation: boolean;
    setEnableSmartNavigation: React.Dispatch<React.SetStateAction<boolean>>;
    modelSupportsReasoning: boolean;
    selectedModelId: string;
  }) => (
    <div className="flex justify-between items-center px-3 py-2 border-t">
      <div className="flex items-center gap-2">
        {/* File Attachment */}
        {/* <BetterTooltip content="파일 첨부">
          <Button
            type="button"
            size="icon"
            variant="ghost"
            className="h-8 w-8 rounded-full"
            disabled={status === 'submitted' || status === 'streaming'}
            onClick={(event) => {
              event.preventDefault();
              fileInputRef.current?.click();
            }}
          >
            <PaperclipIcon className="w-4 h-4" />
            <span className="sr-only">파일 첨부</span>
          </Button>
        </BetterTooltip> */}

        {/* Feature Badges */}
        <div className="flex items-center gap-2">
          {/* AI 추론 배지 - 항상 표시하되 모델 지원 여부에 따라 비활성화 */}
          <AIReasoningCard
            isEnabled={enableThinking}
            isThinking={status === 'streaming'}
            isDisabled={!modelSupportsReasoning}
            disabledReason={getReasoningDisabledMessage(selectedModelId)}
          >
            <Badge
              variant={enableThinking && modelSupportsReasoning ? "ai-active" : "secondary"}
              className={`group relative overflow-hidden ${!modelSupportsReasoning ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
              onClick={() => {
                if (modelSupportsReasoning) {
                  setEnableThinking(!enableThinking);
                }
              }}
            >
              <BrainIcon className={`w-3.5 h-3.5 mr-1.5 ${
                enableThinking && modelSupportsReasoning ? 'text-white' : 'text-gray-600'
              }`} />
              <span className="relative z-10">AI 추론</span>
              {enableThinking && modelSupportsReasoning && status === 'streaming' && (
                <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-white/5 animate-pulse" />
              )}
            </Badge>
          </AIReasoningCard>

          {/* <SmartNavigationHoverCard
            isEnabled={enableSmartNavigation}
          >
            <Badge
              variant={enableSmartNavigation ? "nav-active" : "secondary"}
              className="group relative overflow-hidden"
              onClick={() => setEnableSmartNavigation(!enableSmartNavigation)}
            >
              <NavigationIcon className={`w-3.5 h-3.5 mr-1.5 ${enableSmartNavigation ? 'text-white' : 'text-gray-600'}`} />
              <span className="relative z-10">스마트 지도 제어</span>
              {enableSmartNavigation && (
                <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-white/5" />
              )}
            </Badge>
          </SmartNavigationHoverCard> */}
        </div>
      </div>
    </div>
  ),
  (prevProps, nextProps) => {
    if (prevProps.status !== nextProps.status) return false;
    if (prevProps.enableThinking !== nextProps.enableThinking) return false;
    if (prevProps.enableSmartNavigation !== nextProps.enableSmartNavigation) return false;
    if (prevProps.modelSupportsReasoning !== nextProps.modelSupportsReasoning) return false;
    if (prevProps.selectedModelId !== nextProps.selectedModelId) return false;
    return true;
  },
);

ActionButtons.displayName = 'ActionButtons';

export const ChatMapInput = memo(
  PureChatMapInput,
  (prevProps, nextProps) => {
    if (prevProps.input !== nextProps.input) return false;
    if (prevProps.status !== nextProps.status) return false;
    if (!equal(prevProps.attachments, nextProps.attachments)) return false;
    if (prevProps.enableThinking !== nextProps.enableThinking) return false;
    if (prevProps.enableSmartNavigation !== nextProps.enableSmartNavigation) return false;
    if (prevProps.modelSupportsReasoning !== nextProps.modelSupportsReasoning) return false;
    if (prevProps.selectedModelId !== nextProps.selectedModelId) return false;
    return true;
  },
);
// export const ChatMapInput = PureChatMapInput

// export const ChatMapInput = PureChatMapInput;

// ChatMapInput.displayName = 'ChatMapInput';