import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useScrollToBottom } from "@/lib/hooks/use-scroll-to-bottom";
import { cn } from "@/lib/utils";
import {
  Maximize2,
  Minimize2,
  MessageSquare,
  Layers,
  Share2,
  Users,
} from "lucide-react";
import { TOC } from "@/components/map/toc";
// import { Map } from "@/types/map"; // Replaced by @geon-map/odf types
import { UseMapReturn } from "@geon-map/odf";
import { Attachment, ChatRequestOptions, CreateMessage, Message } from "ai";
import { Dispatch, SetStateAction } from "react";
import { ChatMapInput } from "@/components/chat-map/chat-map-input";
import { Messages } from "@/components/messages";
import { Vote } from "@/lib/db/schema";
import { FooterText } from "../footer";
import { ServerStatus } from "@/components/server-status";
import { HelpMenuBar } from "@/components/chat-map/help-menu-bar";

export default function ChatMapPanel({
  chatId,
  messages,
  isPanelCollapsed,
  togglePanel,
  isPanelMaximized,
  toggleMaximize,
  mapState,
  input,
  setInput,
  handleSubmit,
  stop,
  attachments,
  setAttachments,
  setMessages,
  append,
  isReadonly,
  reload,
  votes,
  status,
  layerConfigs,
  addToolResult,
  error,
  enableThinking,
  setEnableThinking,
  enableSmartNavigation,
  setEnableSmartNavigation,
  modelSupportsReasoning,
  selectedModelId,
}: {
  chatId: string;
  messages: Message[];
  isPanelCollapsed?: boolean;
  togglePanel?: () => void;
  isPanelMaximized?: boolean;
  toggleMaximize?: () => void;
  mapState: UseMapReturn;
  input: string;
  setInput: (value: string) => void;
  handleSubmit: (
    event?: { preventDefault?: () => void },
    chatRequestOptions?: ChatRequestOptions
  ) => void;
  stop: () => void;
  attachments: Array<Attachment>;
  setAttachments: Dispatch<SetStateAction<Array<Attachment>>>;
  setMessages: Dispatch<SetStateAction<Array<Message>>>;
  append: (
    message: Message | CreateMessage,
    chatRequestOptions?: ChatRequestOptions
  ) => Promise<string | null | undefined>;
  isReadonly: boolean;
  reload: (
    chatRequestOptions?: ChatRequestOptions
  ) => Promise<string | null | undefined>;
  votes: Array<Vote> | undefined;
  status: "submitted" | "streaming" | "ready" | "error";
  layerConfigs: any; // TODO: 타입을 LayerProps[]로 변경
  addToolResult: ({
    toolCallId,
    result,
  }: {
    toolCallId: string;
    result: string;
  }) => void;
  error: Error | undefined | null;
  enableThinking: boolean;
  setEnableThinking: Dispatch<SetStateAction<boolean>>;
  enableSmartNavigation: boolean;
  setEnableSmartNavigation: Dispatch<SetStateAction<boolean>>;
  modelSupportsReasoning: boolean;
  selectedModelId: string;
}) {
  const [activeTab, setActiveTab] = useState("chat");
  const [autoSave, setAutoSave] = useState(false);
  const [messagesContainerRef, messagesEndRef] =
    useScrollToBottom<HTMLDivElement>();

  return (
    <div
      className={cn(
        "flex flex-col h-dvh bg-background",
        "transition-all duration-300 ease-in-out",
        isPanelCollapsed ? "hidden" : "w-auto",
        isPanelMaximized ? "border-l border-border" : "rounded-l-lg"
      )}
    >
      {/* Header */}
      <div className="flex items-center justify-between px-4 h-12 border-b border-border bg-secondary/5">
        {/* Left: Title and Server Status */}
        <div className="flex items-center gap-3">
          {/* Server Status */}
          <ServerStatus
            selectedModelId={selectedModelId}
            // className="ml-1"
          />
        </div>

        {/* Right: Controls */}
        <div className="flex items-center gap-2">

          {/* <AutoSave
            mapState={mapState}
            enabled={autoSave}
            onEnabledChange={setAutoSave}
          />
          <SaveButton mapState={mapState} /> */}

          {/* <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => {}}
                disabled={true}
              >
                <Share2 className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Share Map</TooltipContent>
          </Tooltip> */}

          <Button
            variant="ghost"
            size="icon"
            onClick={toggleMaximize}
            className="h-8 w-8"
          >
            {isPanelMaximized ? (
              <Minimize2 className="h-4 w-4" />
            ) : (
              <Maximize2 className="h-4 w-4" />
            )}
          </Button>
        </div>
      </div>

      {/* Tabs */}
      <Tabs
        value={activeTab}
        onValueChange={setActiveTab}
        className="flex-grow flex flex-col min-h-0"
      >
        <TabsList className="flex p-1 gap-1 border-b border-border">
          <TabsTrigger
            value="chat"
            className={cn(
              "flex-1 flex items-center justify-center gap-2",
              "text-sm rounded-md transition-all"
            )}
          >
            <MessageSquare className="h-4 w-4" />
            AI 대화
          </TabsTrigger>
          <TabsTrigger
            value="toc"
            className={cn(
              "flex-1 flex items-center justify-center gap-2",
              "text-sm rounded-md transition-all"
            )}
          >
            <Layers className="h-4 w-4" />
            레이어
          </TabsTrigger>
        </TabsList>

        <TabsContent
          value="chat"
          className="flex flex-col h-full overflow-hidden data-[state=inactive]:hidden mt-0"
        >
          <HelpMenuBar
            setInput={setInput}
            hasMessages={messages.length > 0}
          />
          <Messages
            chatId={chatId}
            setInput={setInput}
            votes={votes}
            messages={messages}
            setMessages={setMessages}
            reload={reload}
            isReadonly={isReadonly}
            status={status}
            error={error} // error prop 전달
            addToolResult={addToolResult}
            mapState={mapState} // mapState 전달
          />

          <form className="flex mx-auto px-4 gap-2 w-full md:max-w-3xl">
            {!isReadonly && (
              <ChatMapInput
                chatId={chatId}
                input={input}
                setInput={setInput}
                handleSubmit={handleSubmit}
                status={status} // isLoading 대신 status 전달
                stop={stop}
                attachments={attachments}
                setAttachments={setAttachments}
                messages={messages}
                setMessages={setMessages}
                append={append}
                enableThinking={enableThinking}
                setEnableThinking={setEnableThinking}
                enableSmartNavigation={enableSmartNavigation}
                setEnableSmartNavigation={setEnableSmartNavigation}
                modelSupportsReasoning={modelSupportsReasoning}
                selectedModelId={selectedModelId}
              />
            )}
          </form>
          <FooterText className="py-2" />
        </TabsContent>

        <TabsContent value="toc" className="flex flex-col">
          <TOC map={mapState?.map} layers={layerConfigs || []} />
        </TabsContent>
      </Tabs>
    </div>
  );
}
