{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/chat-map/layer-list-dialog.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect, useCallback } from \"react\";\nimport {\n  <PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>ontent,\n  <PERSON><PERSON>Header,\n  <PERSON><PERSON>Title,\n  DialogTrigger,\n} from \"@/components/ui/dialog\";\nimport { Button } from \"@/components/ui/button\";\nimport { Badge } from \"@/components/ui/badge\";\n\nimport { Skeleton } from \"@/components/ui/skeleton\";\nimport { Input } from \"@/components/ui/input\";\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\";\nimport { List, Search, Filter, Plus } from \"lucide-react\";\nimport { cn } from \"@/lib/utils\";\nimport { BetterTooltip } from \"../ui/tooltip\";\nimport { toast } from \"sonner\";\nimport { useLayerManager } from \"@/providers/tool-invocation-provider\";\nimport { UseMapReturn } from \"@geon-map/odf\";\n\ninterface LayerItem {\n  lyrId: string;\n  lyrNm: string;\n  lyrTySeCode: string;\n  lyrTySeCodeNm: string;\n  lyrClCodeNm: string;\n  registDt: string;\n  ownerNm: string;\n}\n\ninterface LayerListResponse {\n  code: number;\n  message: string;\n  result: {\n    pageInfo: {\n      pageSize: number;\n      pageIndex: number;\n      totalCount: number;\n    };\n    list: LayerItem[];\n  };\n}\n\nconst getLayerTypeColor = (typeCode: string) => {\n  switch (typeCode) {\n    case \"1\":\n      return \"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300\";\n    case \"2\":\n      return \"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300\";\n    case \"3\":\n      return \"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300\";\n    default:\n      return \"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300\";\n  }\n};\n\nexport function LayerListDialog({ mapState }: { mapState?: UseMapReturn }) {\n  const [open, setOpen] = useState(false);\n  const [layers, setLayers] = useState<LayerItem[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [hasMore, setHasMore] = useState(true);\n  const [pageIndex, setPageIndex] = useState(1);\n  const [totalCount, setTotalCount] = useState(0);\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [typeFilter, setTypeFilter] = useState<string>(\"all\");\n  const [addingLayers, setAddingLayers] = useState<Set<string>>(new Set());\n\n  const { addLayer } = useLayerManager();\n\n  // 레이어 추가 함수\n  const handleAddLayer = async (layer: LayerItem) => {\n    const layerId = layer.lyrId;\n\n    // 이미 추가 중인 레이어인지 확인\n    if (addingLayers.has(layerId)) {\n      return;\n    }\n\n    setAddingLayers(prev => new Set(prev).add(layerId));\n\n    try {\n      // 서버 API를 통해 getLayer 도구 실행\n      const response = await fetch('/api/layers/get-layer', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          lyrId: layerId\n        })\n      });\n\n      if (!response.ok) {\n        throw new Error(`API 요청 실패: ${response.status}`);\n      }\n\n      const result = await response.json();\n\n      if (result && result.id) {\n        // 서울 건물통합정보 레이어인지 확인\n        const isSeoulBuildingLayer = layer.lyrId === \"LR0000004299\" ||\n          layer.lyrNm === \"GIS건물통합정보_서울\" ||\n          result.name === \"GIS건물통합정보_서울\";\n\n        // 레이어 매니저에 추가\n        addLayer({\n          id: result.id,\n          name: result.name || layer.lyrNm,\n          type: result.type || 'wms',\n          visible: true,\n          zIndex: 1,\n          server: result.server,\n          autoFit: isSeoulBuildingLayer ? false : true,\n          layer: result.layer,\n          service: result.service || 'wms',\n          method: result.method || 'get',\n          crtfckey: result.crtfckey,\n          geometryType: result.geometryType,\n          style: result.style,\n          opacity: result.opacity || 1,\n\n          toolCallId: `manual-add-${Date.now()}` // 수동 추가 표시\n        });\n\n        // 서울 건물통합정보 레이어인 경우 지도를 서울 중심으로 이동 (EPSG:5186 표준 서울 중심좌표)\n        if (isSeoulBuildingLayer && mapState) {\n          const center = new odf.Coordinate(955156.7761, 1951925.0984);\n          const newZoom = 12;\n\n          mapState.map.setCenter(center);\n          mapState.map.setZoom(newZoom);\n        }\n        toast.success(`${layer.lyrNm} 레이어가 지도에 추가되었습니다`);\n\n\n        setOpen(false); // 다이얼로그 닫기\n      } else {\n        throw new Error('레이어 정보를 가져올 수 없습니다');\n      }\n    } catch (error) {\n      console.error('레이어 추가 실패:', error);\n      toast.error(`${layer.lyrNm} 레이어 추가에 실패했습니다`);\n    } finally {\n      setAddingLayers(prev => {\n        const newSet = new Set(prev);\n        newSet.delete(layerId);\n        return newSet;\n      });\n    }\n  };\n\n  const fetchLayers = useCallback(async (page: number, reset: boolean = false) => {\n    setLoading(true);\n    try {\n      const params = new URLSearchParams({\n        userId: \"geonuser\",\n        holdDataSeCode: \"0\",\n        pageIndex: page.toString(),\n        pageSize: \"20\",\n      });\n\n      if (searchTerm.trim()) {\n        params.append(\"searchTxt\", searchTerm.trim());\n      }\n\n      if (typeFilter && typeFilter !== \"all\") {\n        params.append(\"lyrTySeCode\", typeFilter);\n      }\n\n      // API 키 추가 (환경변수에서 가져오거나 기본값 사용)\n      const apiKey = process.env.NEXT_PUBLIC_GEON_API_KEY || \"\";\n      if (apiKey) {\n        params.append(\"crtfckey\", apiKey);\n      }\n\n      const response = await fetch(\n        `/api/layers?${params.toString()}`,\n        {\n          method: \"GET\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n          },\n        }\n      );\n\n      if (!response.ok) {\n        throw new Error(`API request failed with status ${response.status}`);\n      }\n\n      const data: LayerListResponse = await response.json();\n\n      if (data.code === 200 && data.result) {\n        const newLayers = data.result.list || [];\n        setLayers(prev => {\n          const updatedLayers = reset ? newLayers : [...prev, ...newLayers];\n          // hasMore 계산을 setLayers 콜백 내부에서 수행\n          setHasMore(newLayers.length === 20 && updatedLayers.length < data.result.pageInfo.totalCount);\n          return updatedLayers;\n        });\n        setTotalCount(data.result.pageInfo.totalCount);\n      }\n    } catch (error) {\n      console.error(\"Failed to fetch layers:\", error);\n    } finally {\n      setLoading(false);\n    }\n  }, [searchTerm, typeFilter]);\n\n  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {\n    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;\n    if (scrollHeight - scrollTop <= clientHeight * 1.5 && hasMore && !loading) {\n      setPageIndex(prev => prev + 1);\n    }\n  }, [hasMore, loading]);\n\n  const handleSearch = useCallback(() => {\n    // 검색 버튼 클릭 시 즉시 검색 (디바운싱 무시)\n    setLayers([]);\n    setPageIndex(1);\n    setHasMore(true);\n    fetchLayers(1, true);\n  }, [fetchLayers]);\n\n  const handleFilterChange = useCallback((value: string) => {\n    setTypeFilter(value);\n    setLayers([]);\n    setPageIndex(1);\n    setHasMore(true);\n    // 필터 변경 시 즉시 새로운 데이터 로드\n    setTimeout(() => fetchLayers(1, true), 0);\n  }, [fetchLayers]);\n\n  // 디바운싱된 검색 실행 함수\n  const debouncedFetch = useCallback(() => {\n    setLayers([]);\n    setPageIndex(1);\n    setHasMore(true);\n    fetchLayers(1, true);\n  }, [fetchLayers]);\n\n  // Dialog가 열릴 때 초기 데이터 로드\n  useEffect(() => {\n    if (open) {\n      setLayers([]);\n      setPageIndex(1);\n      setHasMore(true);\n      fetchLayers(1, true);\n    }\n  }, [open]);\n\n  // 디바운싱: searchTerm이 변경되면 500ms 후에 검색 실행 (Dialog가 열려있고, 초기 로드가 아닐 때만)\n  useEffect(() => {\n    if (!open || searchTerm === \"\") return; // Dialog가 닫혀있거나 검색어가 비어있으면 실행하지 않음\n\n    const timer = setTimeout(() => {\n      debouncedFetch();\n    }, 500);\n\n    return () => clearTimeout(timer);\n  }, [searchTerm, debouncedFetch]);\n\n  // 페이지네이션\n  useEffect(() => {\n    if (pageIndex > 1) {\n      fetchLayers(pageIndex);\n    }\n  }, [pageIndex]);\n\n  return (\n    <Dialog open={open} onOpenChange={setOpen}>\n      <DialogTrigger asChild>\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          className=\"h-9 flex items-center justify-start gap-2 w-full bg-background/80 hover:bg-background/90 backdrop-blur-md shadow-sm border transition-all duration-300\"\n        >\n          <List className=\"h-4 w-4\" />\n          <span className=\"text-sm font-medium\">레이어 검색</span>\n        </Button>\n      </DialogTrigger>\n      <DialogContent className=\"max-w-4xl max-h-[80vh] flex flex-col\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <List className=\"h-5 w-5\" />\n            레이어 목록\n            {totalCount > 0 && (\n              <Badge variant=\"secondary\" className=\"ml-2\">\n                총 {totalCount}개\n              </Badge>\n            )}\n          </DialogTitle>\n        </DialogHeader>\n\n        {/* 검색 및 필터 */}\n        <div className=\"flex gap-2 mb-4\">\n          <div className=\"flex-1 relative\">\n            <Search className={cn(\n              \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4\",\n              loading ? \"text-blue-500 animate-pulse\" : \"text-muted-foreground\"\n            )} />\n            <Input\n              placeholder=\"레이어 이름으로 검색...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              onKeyDown={(e) => e.key === \"Enter\" && handleSearch()}\n              className=\"pl-10\"\n            />\n          </div>\n          <Select value={typeFilter} onValueChange={handleFilterChange}>\n            <SelectTrigger className=\"w-32\">\n              <Filter className=\"h-4 w-4 mr-2\" />\n              <SelectValue placeholder=\"타입\" />\n            </SelectTrigger>\n            <SelectContent>\n              <SelectItem value=\"all\">전체</SelectItem>\n              <SelectItem value=\"1\">점</SelectItem>\n              <SelectItem value=\"2\">선</SelectItem>\n              <SelectItem value=\"3\">면</SelectItem>\n            </SelectContent>\n          </Select>\n          <Button onClick={handleSearch} disabled={loading}>\n            검색\n          </Button>\n        </div>\n\n        {/* 레이어 목록 */}\n        <div className=\"flex-1 h-[400px] overflow-y-auto\" onScroll={handleScroll}>\n          <div className=\"space-y-2 pr-4\">\n            {layers.map((layer) => (\n              <div\n                key={layer.lyrId}\n                className=\"p-3 border rounded-lg hover:bg-accent/50 transition-colors\"\n              >\n                <div className=\"flex items-start justify-between\">\n                  <div className=\"flex-1 min-w-0\">\n                    <div className=\"flex items-center gap-2 mb-1\">\n                      <h4 className=\"font-medium truncate\">{layer.lyrNm}</h4>\n                      <Badge className={cn(\"text-xs\", getLayerTypeColor(layer.lyrTySeCode))}>\n                        {layer.lyrTySeCodeNm}\n                      </Badge>\n                    </div>\n                    <p className=\"text-sm text-muted-foreground mb-1\">\n                      ID: {layer.lyrId}\n                    </p>\n                    <div className=\"flex items-center gap-4 text-xs text-muted-foreground\">\n                      <span>{layer.lyrClCodeNm}</span>\n                      <span>소유자: {layer.ownerNm}</span>\n                      <span>{new Date(layer.registDt).toLocaleDateString()}</span>\n                    </div>\n                  </div>\n\n                  {/* 추가 버튼 */}\n                  <div className=\"flex-shrink-0 ml-3\">\n                    <BetterTooltip content=\"지도에 레이어 추가\">\n                      <Button\n                        variant=\"outline\"\n                        size=\"sm\"\n                        onClick={() => handleAddLayer(layer)}\n                        disabled={addingLayers.has(layer.lyrId)}\n                        className=\"h-8 w-8 p-0\"\n                      >\n                        {addingLayers.has(layer.lyrId) ? (\n                          <div className=\"h-3 w-3 animate-spin rounded-full border border-current border-t-transparent\" />\n                        ) : (\n                          <Plus className=\"h-3 w-3\" />\n                        )}\n                      </Button>\n                    </BetterTooltip>\n                  </div>\n                </div>\n              </div>\n            ))}\n\n            {/* 로딩 스켈레톤 */}\n            {loading && (\n              <div className=\"space-y-2\">\n                {Array.from({ length: 5 }).map((_, i) => (\n                  <div key={i} className=\"p-3 border rounded-lg\">\n                    <div className=\"flex items-center gap-2 mb-2\">\n                      <Skeleton className=\"h-4 w-48\" />\n                      <Skeleton className=\"h-5 w-12\" />\n                    </div>\n                    <Skeleton className=\"h-3 w-32 mb-1\" />\n                    <div className=\"flex gap-4\">\n                      <Skeleton className=\"h-3 w-20\" />\n                      <Skeleton className=\"h-3 w-16\" />\n                      <Skeleton className=\"h-3 w-24\" />\n                    </div>\n                  </div>\n                ))}\n              </div>\n            )}\n\n            {/* 더 이상 데이터가 없을 때 */}\n            {!loading && !hasMore && layers.length > 0 && (\n              <div className=\"text-center py-4 text-muted-foreground\">\n                모든 레이어를 불러왔습니다.\n              </div>\n            )}\n\n            {/* 데이터가 없을 때 */}\n            {!loading && layers.length === 0 && (\n              <div className=\"text-center py-8 text-muted-foreground\">\n                검색 결과가 없습니다.\n              </div>\n            )}\n          </div>\n        </div>\n      </DialogContent>\n    </Dialog>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAOA;AACA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AApBA;;;;;;;;;;;;;;AA8CA,MAAM,oBAAoB,CAAC;IACzB,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,SAAS,gBAAgB,EAAE,QAAQ,EAA+B;IACvE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IACpD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAU;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IAElE,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,4IAAA,CAAA,kBAAe,AAAD;IAEnC,YAAY;IACZ,MAAM,iBAAiB,OAAO;QAC5B,MAAM,UAAU,MAAM,KAAK;QAE3B,oBAAoB;QACpB,IAAI,aAAa,GAAG,CAAC,UAAU;YAC7B;QACF;QAEA,gBAAgB,CAAA,OAAQ,IAAI,IAAI,MAAM,GAAG,CAAC;QAE1C,IAAI;YACF,4BAA4B;YAC5B,MAAM,WAAW,MAAM,MAAM,yBAAyB;gBACpD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,OAAO;gBACT;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,SAAS,MAAM,EAAE;YACjD;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,UAAU,OAAO,EAAE,EAAE;gBACvB,qBAAqB;gBACrB,MAAM,uBAAuB,MAAM,KAAK,KAAK,kBAC3C,MAAM,KAAK,KAAK,kBAChB,OAAO,IAAI,KAAK;gBAElB,cAAc;gBACd,SAAS;oBACP,IAAI,OAAO,EAAE;oBACb,MAAM,OAAO,IAAI,IAAI,MAAM,KAAK;oBAChC,MAAM,OAAO,IAAI,IAAI;oBACrB,SAAS;oBACT,QAAQ;oBACR,QAAQ,OAAO,MAAM;oBACrB,SAAS,uBAAuB,QAAQ;oBACxC,OAAO,OAAO,KAAK;oBACnB,SAAS,OAAO,OAAO,IAAI;oBAC3B,QAAQ,OAAO,MAAM,IAAI;oBACzB,UAAU,OAAO,QAAQ;oBACzB,cAAc,OAAO,YAAY;oBACjC,OAAO,OAAO,KAAK;oBACnB,SAAS,OAAO,OAAO,IAAI;oBAE3B,YAAY,CAAC,WAAW,EAAE,KAAK,GAAG,IAAI,CAAC,WAAW;gBACpD;gBAEA,0DAA0D;gBAC1D,IAAI,wBAAwB,UAAU;oBACpC,MAAM,SAAS,IAAI,IAAI,UAAU,CAAC,aAAa;oBAC/C,MAAM,UAAU;oBAEhB,SAAS,GAAG,CAAC,SAAS,CAAC;oBACvB,SAAS,GAAG,CAAC,OAAO,CAAC;gBACvB;gBACA,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,GAAG,MAAM,KAAK,CAAC,iBAAiB,CAAC;gBAG/C,QAAQ,QAAQ,WAAW;YAC7B,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,cAAc;YAC5B,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,GAAG,MAAM,KAAK,CAAC,eAAe,CAAC;QAC7C,SAAU;YACR,gBAAgB,CAAA;gBACd,MAAM,SAAS,IAAI,IAAI;gBACvB,OAAO,MAAM,CAAC;gBACd,OAAO;YACT;QACF;IACF;IAEA,MAAM,cAAc,CAAA,GAAA,8SAAA,CAAA,cAAW,AAAD,EAAE,OAAO,MAAc,QAAiB,KAAK;QACzE,WAAW;QACX,IAAI;YACF,MAAM,SAAS,IAAI,gBAAgB;gBACjC,QAAQ;gBACR,gBAAgB;gBAChB,WAAW,KAAK,QAAQ;gBACxB,UAAU;YACZ;YAEA,IAAI,WAAW,IAAI,IAAI;gBACrB,OAAO,MAAM,CAAC,aAAa,WAAW,IAAI;YAC5C;YAEA,IAAI,cAAc,eAAe,OAAO;gBACtC,OAAO,MAAM,CAAC,eAAe;YAC/B;YAEA,iCAAiC;YACjC,MAAM,SAAS,QAAQ,GAAG,CAAC,wBAAwB,IAAI;YACvD,IAAI,QAAQ;gBACV,OAAO,MAAM,CAAC,YAAY;YAC5B;YAEA,MAAM,WAAW,MAAM,MACrB,CAAC,YAAY,EAAE,OAAO,QAAQ,IAAI,EAClC;gBACE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAGF,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,+BAA+B,EAAE,SAAS,MAAM,EAAE;YACrE;YAEA,MAAM,OAA0B,MAAM,SAAS,IAAI;YAEnD,IAAI,KAAK,IAAI,KAAK,OAAO,KAAK,MAAM,EAAE;gBACpC,MAAM,YAAY,KAAK,MAAM,CAAC,IAAI,IAAI,EAAE;gBACxC,UAAU,CAAA;oBACR,MAAM,gBAAgB,QAAQ,YAAY;2BAAI;2BAAS;qBAAU;oBACjE,mCAAmC;oBACnC,WAAW,UAAU,MAAM,KAAK,MAAM,cAAc,MAAM,GAAG,KAAK,MAAM,CAAC,QAAQ,CAAC,UAAU;oBAC5F,OAAO;gBACT;gBACA,cAAc,KAAK,MAAM,CAAC,QAAQ,CAAC,UAAU;YAC/C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,WAAW;QACb;IACF,GAAG;QAAC;QAAY;KAAW;IAE3B,MAAM,eAAe,CAAA,GAAA,8SAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAChC,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,YAAY,EAAE,GAAG,EAAE,aAAa;QACjE,IAAI,eAAe,aAAa,eAAe,OAAO,WAAW,CAAC,SAAS;YACzE,aAAa,CAAA,OAAQ,OAAO;QAC9B;IACF,GAAG;QAAC;QAAS;KAAQ;IAErB,MAAM,eAAe,CAAA,GAAA,8SAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,6BAA6B;QAC7B,UAAU,EAAE;QACZ,aAAa;QACb,WAAW;QACX,YAAY,GAAG;IACjB,GAAG;QAAC;KAAY;IAEhB,MAAM,qBAAqB,CAAA,GAAA,8SAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACtC,cAAc;QACd,UAAU,EAAE;QACZ,aAAa;QACb,WAAW;QACX,wBAAwB;QACxB,WAAW,IAAM,YAAY,GAAG,OAAO;IACzC,GAAG;QAAC;KAAY;IAEhB,iBAAiB;IACjB,MAAM,iBAAiB,CAAA,GAAA,8SAAA,CAAA,cAAW,AAAD,EAAE;QACjC,UAAU,EAAE;QACZ,aAAa;QACb,WAAW;QACX,YAAY,GAAG;IACjB,GAAG;QAAC;KAAY;IAEhB,yBAAyB;IACzB,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR,UAAU,EAAE;YACZ,aAAa;YACb,WAAW;YACX,YAAY,GAAG;QACjB;IACF,GAAG;QAAC;KAAK;IAET,qEAAqE;IACrE,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,QAAQ,eAAe,IAAI,QAAQ,mCAAmC;QAE3E,MAAM,QAAQ,WAAW;YACvB;QACF,GAAG;QAEH,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;QAAY;KAAe;IAE/B,SAAS;IACT,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY,GAAG;YACjB,YAAY;QACd;IACF,GAAG;QAAC;KAAU;IAEd,qBACE,uVAAC,2HAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;;0BAChC,uVAAC,2HAAA,CAAA,gBAAa;gBAAC,OAAO;0BACpB,cAAA,uVAAC,2HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;;sCAEV,uVAAC,sRAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,uVAAC;4BAAK,WAAU;sCAAsB;;;;;;;;;;;;;;;;;0BAG1C,uVAAC,2HAAA,CAAA,gBAAa;gBAAC,WAAU;;kCACvB,uVAAC,2HAAA,CAAA,eAAY;kCACX,cAAA,uVAAC,2HAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,uVAAC,sRAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAY;gCAE3B,aAAa,mBACZ,uVAAC,0HAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAY,WAAU;;wCAAO;wCACvC;wCAAW;;;;;;;;;;;;;;;;;;kCAOtB,uVAAC;wBAAI,WAAU;;0CACb,uVAAC;gCAAI,WAAU;;kDACb,uVAAC,0RAAA,CAAA,SAAM;wCAAC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAClB,8DACA,UAAU,gCAAgC;;;;;;kDAE5C,uVAAC,0HAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAW,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;wCACvC,WAAU;;;;;;;;;;;;0CAGd,uVAAC,2HAAA,CAAA,SAAM;gCAAC,OAAO;gCAAY,eAAe;;kDACxC,uVAAC,2HAAA,CAAA,gBAAa;wCAAC,WAAU;;0DACvB,uVAAC,0RAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,uVAAC,2HAAA,CAAA,cAAW;gDAAC,aAAY;;;;;;;;;;;;kDAE3B,uVAAC,2HAAA,CAAA,gBAAa;;0DACZ,uVAAC,2HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAM;;;;;;0DACxB,uVAAC,2HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAI;;;;;;0DACtB,uVAAC,2HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAI;;;;;;0DACtB,uVAAC,2HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAI;;;;;;;;;;;;;;;;;;0CAG1B,uVAAC,2HAAA,CAAA,SAAM;gCAAC,SAAS;gCAAc,UAAU;0CAAS;;;;;;;;;;;;kCAMpD,uVAAC;wBAAI,WAAU;wBAAmC,UAAU;kCAC1D,cAAA,uVAAC;4BAAI,WAAU;;gCACZ,OAAO,GAAG,CAAC,CAAC,sBACX,uVAAC;wCAEC,WAAU;kDAEV,cAAA,uVAAC;4CAAI,WAAU;;8DACb,uVAAC;oDAAI,WAAU;;sEACb,uVAAC;4DAAI,WAAU;;8EACb,uVAAC;oEAAG,WAAU;8EAAwB,MAAM,KAAK;;;;;;8EACjD,uVAAC,0HAAA,CAAA,QAAK;oEAAC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,WAAW,kBAAkB,MAAM,WAAW;8EAChE,MAAM,aAAa;;;;;;;;;;;;sEAGxB,uVAAC;4DAAE,WAAU;;gEAAqC;gEAC3C,MAAM,KAAK;;;;;;;sEAElB,uVAAC;4DAAI,WAAU;;8EACb,uVAAC;8EAAM,MAAM,WAAW;;;;;;8EACxB,uVAAC;;wEAAK;wEAAM,MAAM,OAAO;;;;;;;8EACzB,uVAAC;8EAAM,IAAI,KAAK,MAAM,QAAQ,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;8DAKtD,uVAAC;oDAAI,WAAU;8DACb,cAAA,uVAAC,4HAAA,CAAA,gBAAa;wDAAC,SAAQ;kEACrB,cAAA,uVAAC,2HAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS,IAAM,eAAe;4DAC9B,UAAU,aAAa,GAAG,CAAC,MAAM,KAAK;4DACtC,WAAU;sEAET,aAAa,GAAG,CAAC,MAAM,KAAK,kBAC3B,uVAAC;gEAAI,WAAU;;;;;qFAEf,uVAAC,sRAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;uCAlCrB,MAAM,KAAK;;;;;gCA4CnB,yBACC,uVAAC;oCAAI,WAAU;8CACZ,MAAM,IAAI,CAAC;wCAAE,QAAQ;oCAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,uVAAC;4CAAY,WAAU;;8DACrB,uVAAC;oDAAI,WAAU;;sEACb,uVAAC,6HAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,uVAAC,6HAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;;;;;;;8DAEtB,uVAAC,6HAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,uVAAC;oDAAI,WAAU;;sEACb,uVAAC,6HAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,uVAAC,6HAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,uVAAC,6HAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;;;;;;;;2CATd;;;;;;;;;;gCAiBf,CAAC,WAAW,CAAC,WAAW,OAAO,MAAM,GAAG,mBACvC,uVAAC;oCAAI,WAAU;8CAAyC;;;;;;gCAMzD,CAAC,WAAW,OAAO,MAAM,KAAK,mBAC7B,uVAAC;oCAAI,WAAU;8CAAyC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStE", "debugId": null}}, {"offset": {"line": 705, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/chat-map/chat-map-input.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport {\r\n  useState,\r\n  useRef,\r\n  useEffect,\r\n  useCallback,\r\n  type Dispatch,\r\n  type SetStateAction,\r\n  type ChangeEvent,\r\n  memo,\r\n} from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport { useLocalStorage, useWindowSize } from 'usehooks-ts';\r\n\r\nimport { toast } from 'sonner';\r\nimport equal from 'fast-deep-equal';\r\nimport {\r\n  Attachment,\r\n  ChatRequestOptions,\r\n  CreateMessage,\r\n  Message,\r\n} from 'ai';\r\n\r\nimport {\r\n  BetterTooltip,\r\n} from '@/components/ui/tooltip';\r\nimport { Button } from '@/components/ui/button';\r\nimport { IconSpinner, StopIcon } from '@/components/ui/icons';\r\nimport { ForwardIcon, PaperclipIcon, PlusIcon, BrainIcon, SearchIcon, NavigationIcon } from 'lucide-react';\r\nimport { PreviewAttachment } from '@/components/preview-attachment';\r\nimport { useSidebar } from '../ui/sidebar';\r\nimport { SuggestedActions } from './suggested-actions';\r\nimport { Textarea } from '../ui/textarea';\r\nimport { Switch } from '@/components/ui/switch';\r\nimport { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { HoverCard, HoverCardContent, HoverCardTrigger } from '@/components/ui/hover-card';\r\nimport { SmartNavigationHoverCard } from '@/components/ui/magic-hover-card';\r\nimport { AIReasoningCard } from '@/components/ui/ai-reasoning-card';\r\nimport { getReasoningDisabledMessage } from '@/lib/ai/models';\r\nimport { chat } from '@/lib/db/schema';\r\n\r\nfunction PureChatMapInput({\r\n  chatId,\r\n  input,\r\n  setInput,\r\n  status,\r\n  stop,\r\n  attachments,\r\n  setAttachments,\r\n  messages,\r\n  setMessages,\r\n  append,\r\n  handleSubmit,\r\n  enableThinking,\r\n  setEnableThinking,\r\n  enableSmartNavigation,\r\n  setEnableSmartNavigation,\r\n  modelSupportsReasoning,\r\n  selectedModelId,\r\n}: {\r\n  chatId: string;\r\n  input: string;\r\n  setInput: (value: string) => void;\r\n  status: 'submitted' | 'streaming' | 'ready' | 'error';\r\n  stop: () => void;\r\n  attachments: Array<Attachment>;\r\n  setAttachments: Dispatch<SetStateAction<Array<Attachment>>>;\r\n  messages: Array<Message>;\r\n  setMessages: Dispatch<SetStateAction<Array<Message>>>;\r\n  append: (\r\n    message: Message | CreateMessage,\r\n    chatRequestOptions?: ChatRequestOptions,\r\n  ) => Promise<string | null | undefined>;\r\n  handleSubmit: (\r\n    event?: {\r\n      preventDefault?: () => void;\r\n    },\r\n    chatRequestOptions?: ChatRequestOptions,\r\n  ) => void;\r\n  enableThinking: boolean;\r\n  setEnableThinking: React.Dispatch<React.SetStateAction<boolean>>;\r\n  enableSmartNavigation: boolean;\r\n  setEnableSmartNavigation: React.Dispatch<React.SetStateAction<boolean>>;\r\n  modelSupportsReasoning: boolean;\r\n  selectedModelId: string;\r\n}) {\r\n  const textareaRef = useRef<HTMLTextAreaElement>(null);\r\n  const router = useRouter();\r\n  const { setOpenMobile } = useSidebar();\r\n  const { width } = useWindowSize();\r\n  const fileInputRef = useRef<HTMLInputElement | null>(null);\r\n  const [uploadQueue, setUploadQueue] = useState<Array<string>>([]);\r\n\r\n  const [localStorageInput, setLocalStorageInput] = useLocalStorage('input', '');\r\n\r\n  const adjustHeight = () => {\r\n    if (textareaRef.current) {\r\n      textareaRef.current.style.height = 'auto';\r\n      textareaRef.current.style.height = `${textareaRef.current.scrollHeight + 2}px`;\r\n    }\r\n  };\r\n\r\n\r\n  useEffect(() => {\r\n    // if (textareaRef.current) {\r\n    // adjustHeight();\r\n    // }\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (textareaRef.current) {\r\n      const domValue = textareaRef.current.value;\r\n      const finalValue = domValue || localStorageInput || '';\r\n      setInput(finalValue);\r\n      // adjustHeight();\r\n    }\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    setLocalStorageInput(input);\r\n  }, [input, setLocalStorageInput]);\r\n\r\n\r\n  const handleInput = useCallback((event: React.ChangeEvent<HTMLTextAreaElement>) => {\r\n    setInput(event.target.value);\r\n    adjustHeight();\r\n  }, [setInput]);\r\n\r\n  const submitForm = useCallback(() => {\r\n    window.history.replaceState({}, '', `/geon-2d-map/${chatId}`);\r\n\r\n    handleSubmit(undefined, {\r\n      experimental_attachments: attachments,\r\n    });\r\n\r\n    setAttachments([]);\r\n    setLocalStorageInput('');\r\n\r\n    if (width && width > 768) {\r\n      textareaRef.current?.focus();\r\n    }\r\n  }, [\r\n    attachments,\r\n    handleSubmit,\r\n    setAttachments,\r\n    setLocalStorageInput,\r\n    width,\r\n    chatId,\r\n  ]);\r\n\r\n  const uploadFile = async (file: File) => {\r\n    const formData = new FormData();\r\n    formData.append('file', file);\r\n\r\n    try {\r\n      const response = await fetch('/api/files/upload', {\r\n        method: 'POST',\r\n        body: formData,\r\n      });\r\n\r\n      if (response.ok) {\r\n        const data = await response.json();\r\n        const { url, pathname, contentType } = data;\r\n        return { url, name: pathname, contentType };\r\n      }\r\n      const { error } = await response.json();\r\n      toast.error(error);\r\n    } catch (error) {\r\n      toast.error('Failed to upload file, please try again!');\r\n    }\r\n  };\r\n\r\n  const handleFileChange = useCallback(\r\n    async (event: ChangeEvent<HTMLInputElement>) => {\r\n      const files = Array.from(event.target.files || []);\r\n      setUploadQueue(files.map((file) => file.name));\r\n\r\n      try {\r\n        const uploadPromises = files.map((file) => uploadFile(file));\r\n        const uploadedAttachments = await Promise.all(uploadPromises);\r\n        const successfullyUploadedAttachments = uploadedAttachments.filter(\r\n          (attachment) => attachment !== undefined,\r\n        );\r\n\r\n        setAttachments((currentAttachments) => [\r\n          ...currentAttachments,\r\n          ...successfullyUploadedAttachments,\r\n        ]);\r\n      } catch (error) {\r\n        console.error('Error uploading files!', error);\r\n      } finally {\r\n        setUploadQueue([]);\r\n      }\r\n    },\r\n    [setAttachments],\r\n  );\r\n\r\n  const removeFile = useCallback((url: string) => {\r\n    setAttachments((prevFiles) => prevFiles.filter((value) => value.url !== url));\r\n  }, [setAttachments]);\r\n\r\n  return (\r\n    <div className=\"relative w-full flex flex-1 flex-col gap-4\">\r\n      {/* {messages.length === 0 && attachments.length === 0 && uploadQueue.length === 0 && (\r\n        <SuggestedActions setInput={setInput} append={append} chatId={chatId} />\r\n      )} */}\r\n\r\n      <input\r\n        type=\"file\"\r\n        className=\"fixed -top-4 -left-4 size-0.5 opacity-0 pointer-events-none\"\r\n        ref={fileInputRef}\r\n        multiple\r\n        onChange={handleFileChange}\r\n        tabIndex={-1}\r\n      />\r\n\r\n      {(attachments.length > 0 || uploadQueue.length > 0) && (\r\n        <div className=\"flex flex-wrap gap-2 p-2 bg-background rounded-lg\">\r\n          {attachments.map((attachment) => (\r\n            <PreviewAttachment\r\n              key={attachment.url}\r\n              attachment={attachment}\r\n              removeFile={removeFile}\r\n            />\r\n          ))}\r\n\r\n          {uploadQueue.map((filename) => (\r\n            <PreviewAttachment\r\n              key={filename}\r\n              attachment={{\r\n                url: '',\r\n                name: filename,\r\n                contentType: '',\r\n              }}\r\n              isUploading={true}\r\n            />\r\n          ))}\r\n        </div>\r\n      )}\r\n\r\n      <div className=\"flex flex-col rounded-2xl border bg-background shadow-sm\">\r\n        <div className=\"relative\">\r\n          <Textarea\r\n            ref={textareaRef}\r\n            placeholder=\"원하는 지역, 레이어 등을 입력해주세요.\"\r\n            value={input}\r\n            onChange={handleInput}\r\n            className=\"min-h-[48px] max-h-[40vh] overflow-hidden resize-none rounded-2xl !text-sm p-4 pr-16 w-full border-0 focus-visible:ring-0 focus-visible:ring-offset-0\"\r\n            rows={1}\r\n            onKeyDown={(event) => {\r\n              if (event.key === 'Enter' && !event.shiftKey) {\r\n                event.preventDefault();\r\n                if (status === 'submitted' || status === 'streaming') {\r\n                  toast.error('이전 응답이 완료될 때까지 잠시만 기다려 주세요!');\r\n                } else {\r\n                  submitForm();\r\n                }\r\n              }\r\n            }}\r\n          />\r\n          {/* Send Button - positioned absolutely in the input */}\r\n          <div className=\"absolute right-2 top-1/2 -translate-y-1/2\">\r\n            {(status === 'submitted' || status === 'streaming') ? (\r\n              <Button\r\n                size=\"icon\"\r\n                variant=\"ghost\"\r\n                className=\"h-8 w-8 rounded-full\"\r\n                type='button'\r\n                onClick={(event) => {\r\n                  event.preventDefault();\r\n                  stop();\r\n                }}\r\n              >\r\n                <StopIcon size={16} />\r\n              </Button>\r\n            ) : (\r\n              <Button\r\n                size=\"icon\"\r\n                className=\"h-8 w-8 rounded-full\"\r\n                onClick={(event) => {\r\n                  submitForm();\r\n                }}\r\n                disabled={input.length === 0 || uploadQueue.length > 0}\r\n              >\r\n                <ForwardIcon size={16} />\r\n              </Button>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        <ActionButtons\r\n          status={status}\r\n          fileInputRef={fileInputRef}\r\n          enableThinking={enableThinking}\r\n          setEnableThinking={setEnableThinking}\r\n          enableSmartNavigation={enableSmartNavigation}\r\n          setEnableSmartNavigation={setEnableSmartNavigation}\r\n          modelSupportsReasoning={modelSupportsReasoning}\r\n          selectedModelId={selectedModelId}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nconst ActionButtons = memo(\r\n  ({\r\n    status,\r\n    fileInputRef,\r\n    enableThinking,\r\n    setEnableThinking,\r\n    enableSmartNavigation,\r\n    setEnableSmartNavigation,\r\n    modelSupportsReasoning,\r\n    selectedModelId,\r\n  }: {\r\n    status: 'submitted' | 'streaming' | 'ready' | 'error';\r\n    fileInputRef: React.RefObject<HTMLInputElement | null>;\r\n    enableThinking: boolean;\r\n    setEnableThinking: React.Dispatch<React.SetStateAction<boolean>>;\r\n    enableSmartNavigation: boolean;\r\n    setEnableSmartNavigation: React.Dispatch<React.SetStateAction<boolean>>;\r\n    modelSupportsReasoning: boolean;\r\n    selectedModelId: string;\r\n  }) => (\r\n    <div className=\"flex justify-between items-center px-3 py-2 border-t\">\r\n      <div className=\"flex items-center gap-2\">\r\n        {/* File Attachment */}\r\n        {/* <BetterTooltip content=\"파일 첨부\">\r\n          <Button\r\n            type=\"button\"\r\n            size=\"icon\"\r\n            variant=\"ghost\"\r\n            className=\"h-8 w-8 rounded-full\"\r\n            disabled={status === 'submitted' || status === 'streaming'}\r\n            onClick={(event) => {\r\n              event.preventDefault();\r\n              fileInputRef.current?.click();\r\n            }}\r\n          >\r\n            <PaperclipIcon className=\"w-4 h-4\" />\r\n            <span className=\"sr-only\">파일 첨부</span>\r\n          </Button>\r\n        </BetterTooltip> */}\r\n\r\n        {/* Feature Badges */}\r\n        <div className=\"flex items-center gap-2\">\r\n          {/* AI 추론 배지 - 항상 표시하되 모델 지원 여부에 따라 비활성화 */}\r\n          <AIReasoningCard\r\n            isEnabled={enableThinking}\r\n            isThinking={status === 'streaming'}\r\n            isDisabled={!modelSupportsReasoning}\r\n            disabledReason={getReasoningDisabledMessage(selectedModelId)}\r\n          >\r\n            <Badge\r\n              variant={enableThinking && modelSupportsReasoning ? \"ai-active\" : \"secondary\"}\r\n              className={`group relative overflow-hidden ${!modelSupportsReasoning ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}\r\n              onClick={() => {\r\n                if (modelSupportsReasoning) {\r\n                  setEnableThinking(!enableThinking);\r\n                }\r\n              }}\r\n            >\r\n              <BrainIcon className={`w-3.5 h-3.5 mr-1.5 ${\r\n                enableThinking && modelSupportsReasoning ? 'text-white' : 'text-gray-600'\r\n              }`} />\r\n              <span className=\"relative z-10\">AI 추론</span>\r\n              {enableThinking && modelSupportsReasoning && status === 'streaming' && (\r\n                <div className=\"absolute inset-0 bg-gradient-to-r from-white/10 to-white/5 animate-pulse\" />\r\n              )}\r\n            </Badge>\r\n          </AIReasoningCard>\r\n\r\n          {/* <SmartNavigationHoverCard\r\n            isEnabled={enableSmartNavigation}\r\n          >\r\n            <Badge\r\n              variant={enableSmartNavigation ? \"nav-active\" : \"secondary\"}\r\n              className=\"group relative overflow-hidden\"\r\n              onClick={() => setEnableSmartNavigation(!enableSmartNavigation)}\r\n            >\r\n              <NavigationIcon className={`w-3.5 h-3.5 mr-1.5 ${enableSmartNavigation ? 'text-white' : 'text-gray-600'}`} />\r\n              <span className=\"relative z-10\">스마트 지도 제어</span>\r\n              {enableSmartNavigation && (\r\n                <div className=\"absolute inset-0 bg-gradient-to-r from-white/10 to-white/5\" />\r\n              )}\r\n            </Badge>\r\n          </SmartNavigationHoverCard> */}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  ),\r\n  (prevProps, nextProps) => {\r\n    if (prevProps.status !== nextProps.status) return false;\r\n    if (prevProps.enableThinking !== nextProps.enableThinking) return false;\r\n    if (prevProps.enableSmartNavigation !== nextProps.enableSmartNavigation) return false;\r\n    if (prevProps.modelSupportsReasoning !== nextProps.modelSupportsReasoning) return false;\r\n    if (prevProps.selectedModelId !== nextProps.selectedModelId) return false;\r\n    return true;\r\n  },\r\n);\r\n\r\nActionButtons.displayName = 'ActionButtons';\r\n\r\nexport const ChatMapInput = memo(\r\n  PureChatMapInput,\r\n  (prevProps, nextProps) => {\r\n    if (prevProps.input !== nextProps.input) return false;\r\n    if (prevProps.status !== nextProps.status) return false;\r\n    if (!equal(prevProps.attachments, nextProps.attachments)) return false;\r\n    if (prevProps.enableThinking !== nextProps.enableThinking) return false;\r\n    if (prevProps.enableSmartNavigation !== nextProps.enableSmartNavigation) return false;\r\n    if (prevProps.modelSupportsReasoning !== nextProps.modelSupportsReasoning) return false;\r\n    if (prevProps.selectedModelId !== nextProps.selectedModelId) return false;\r\n    return true;\r\n  },\r\n);\r\n// export const ChatMapInput = PureChatMapInput\r\n\r\n// export const ChatMapInput = PureChatMapInput;\r\n\r\n// ChatMapInput.displayName = 'ChatMapInput';"], "names": [], "mappings": ";;;;AAGA;AAUA;AACA;AAEA;AACA;AAWA;AACA;AACA;AAAA;AACA;AACA;AAEA;AAGA;AAGA;AACA;AAzCA;;;;;;;;;;;;;;;;AA4CA,SAAS,iBAAiB,EACxB,MAAM,EACN,KAAK,EACL,QAAQ,EACR,MAAM,EACN,IAAI,EACJ,WAAW,EACX,cAAc,EACd,QAAQ,EACR,WAAW,EACX,MAAM,EACN,YAAY,EACZ,cAAc,EACd,iBAAiB,EACjB,qBAAqB,EACrB,wBAAwB,EACxB,sBAAsB,EACtB,eAAe,EA2BhB;IACC,MAAM,cAAc,CAAA,GAAA,8SAAA,CAAA,SAAM,AAAD,EAAuB;IAChD,MAAM,SAAS,CAAA,GAAA,2OAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,aAAU,AAAD;IACnC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,gOAAA,CAAA,gBAAa,AAAD;IAC9B,MAAM,eAAe,CAAA,GAAA,8SAAA,CAAA,SAAM,AAAD,EAA2B;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAEhE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,gOAAA,CAAA,kBAAe,AAAD,EAAE,SAAS;IAE3E,MAAM,eAAe;QACnB,IAAI,YAAY,OAAO,EAAE;YACvB,YAAY,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG;YACnC,YAAY,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,YAAY,OAAO,CAAC,YAAY,GAAG,EAAE,EAAE,CAAC;QAChF;IACF;IAGA,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EAAE;IACR,6BAA6B;IAC7B,kBAAkB;IAClB,IAAI;IACN,GAAG,EAAE;IAEL,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY,OAAO,EAAE;YACvB,MAAM,WAAW,YAAY,OAAO,CAAC,KAAK;YAC1C,MAAM,aAAa,YAAY,qBAAqB;YACpD,SAAS;QACT,kBAAkB;QACpB;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EAAE;QACR,qBAAqB;IACvB,GAAG;QAAC;QAAO;KAAqB;IAGhC,MAAM,cAAc,CAAA,GAAA,8SAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/B,SAAS,MAAM,MAAM,CAAC,KAAK;QAC3B;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,aAAa,CAAA,GAAA,8SAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,OAAO,OAAO,CAAC,YAAY,CAAC,CAAC,GAAG,IAAI,CAAC,aAAa,EAAE,QAAQ;QAE5D,aAAa,WAAW;YACtB,0BAA0B;QAC5B;QAEA,eAAe,EAAE;QACjB,qBAAqB;QAErB,IAAI,SAAS,QAAQ,KAAK;YACxB,YAAY,OAAO,EAAE;QACvB;IACF,GAAG;QACD;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,aAAa,OAAO;QACxB,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QAExB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,qBAAqB;gBAChD,QAAQ;gBACR,MAAM;YACR;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG;gBACvC,OAAO;oBAAE;oBAAK,MAAM;oBAAU;gBAAY;YAC5C;YACA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI;YACrC,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,EAAE,OAAO,OAAO;YACd,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,mBAAmB,CAAA,GAAA,8SAAA,CAAA,cAAW,AAAD,EACjC,OAAO;QACL,MAAM,QAAQ,MAAM,IAAI,CAAC,MAAM,MAAM,CAAC,KAAK,IAAI,EAAE;QACjD,eAAe,MAAM,GAAG,CAAC,CAAC,OAAS,KAAK,IAAI;QAE5C,IAAI;YACF,MAAM,iBAAiB,MAAM,GAAG,CAAC,CAAC,OAAS,WAAW;YACtD,MAAM,sBAAsB,MAAM,QAAQ,GAAG,CAAC;YAC9C,MAAM,kCAAkC,oBAAoB,MAAM,CAChE,CAAC,aAAe,eAAe;YAGjC,eAAe,CAAC,qBAAuB;uBAClC;uBACA;iBACJ;QACH,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,eAAe,EAAE;QACnB;IACF,GACA;QAAC;KAAe;IAGlB,MAAM,aAAa,CAAA,GAAA,8SAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC9B,eAAe,CAAC,YAAc,UAAU,MAAM,CAAC,CAAC,QAAU,MAAM,GAAG,KAAK;IAC1E,GAAG;QAAC;KAAe;IAEnB,qBACE,uVAAC;QAAI,WAAU;;0BAKb,uVAAC;gBACC,MAAK;gBACL,WAAU;gBACV,KAAK;gBACL,QAAQ;gBACR,UAAU;gBACV,UAAU,CAAC;;;;;;YAGZ,CAAC,YAAY,MAAM,GAAG,KAAK,YAAY,MAAM,GAAG,CAAC,mBAChD,uVAAC;gBAAI,WAAU;;oBACZ,YAAY,GAAG,CAAC,CAAC,2BAChB,uVAAC,oIAAA,CAAA,oBAAiB;4BAEhB,YAAY;4BACZ,YAAY;2BAFP,WAAW,GAAG;;;;;oBAMtB,YAAY,GAAG,CAAC,CAAC,yBAChB,uVAAC,oIAAA,CAAA,oBAAiB;4BAEhB,YAAY;gCACV,KAAK;gCACL,MAAM;gCACN,aAAa;4BACf;4BACA,aAAa;2BANR;;;;;;;;;;;0BAYb,uVAAC;gBAAI,WAAU;;kCACb,uVAAC;wBAAI,WAAU;;0CACb,uVAAC,6HAAA,CAAA,WAAQ;gCACP,KAAK;gCACL,aAAY;gCACZ,OAAO;gCACP,UAAU;gCACV,WAAU;gCACV,MAAM;gCACN,WAAW,CAAC;oCACV,IAAI,MAAM,GAAG,KAAK,WAAW,CAAC,MAAM,QAAQ,EAAE;wCAC5C,MAAM,cAAc;wCACpB,IAAI,WAAW,eAAe,WAAW,aAAa;4CACpD,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;wCACd,OAAO;4CACL;wCACF;oCACF;gCACF;;;;;;0CAGF,uVAAC;gCAAI,WAAU;0CACZ,AAAC,WAAW,eAAe,WAAW,4BACrC,uVAAC,2HAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,WAAU;oCACV,MAAK;oCACL,SAAS,CAAC;wCACR,MAAM,cAAc;wCACpB;oCACF;8CAEA,cAAA,uVAAC,0HAAA,CAAA,WAAQ;wCAAC,MAAM;;;;;;;;;;yDAGlB,uVAAC,2HAAA,CAAA,SAAM;oCACL,MAAK;oCACL,WAAU;oCACV,SAAS,CAAC;wCACR;oCACF;oCACA,UAAU,MAAM,MAAM,KAAK,KAAK,YAAY,MAAM,GAAG;8CAErD,cAAA,uVAAC,gSAAA,CAAA,cAAW;wCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;kCAM3B,uVAAC;wBACC,QAAQ;wBACR,cAAc;wBACd,gBAAgB;wBAChB,mBAAmB;wBACnB,uBAAuB;wBACvB,0BAA0B;wBAC1B,wBAAwB;wBACxB,iBAAiB;;;;;;;;;;;;;;;;;;AAK3B;AAEA,MAAM,8BAAgB,CAAA,GAAA,8SAAA,CAAA,OAAI,AAAD,EACvB,CAAC,EACC,MAAM,EACN,YAAY,EACZ,cAAc,EACd,iBAAiB,EACjB,qBAAqB,EACrB,wBAAwB,EACxB,sBAAsB,EACtB,eAAe,EAUhB,iBACC,uVAAC;QAAI,WAAU;kBACb,cAAA,uVAAC;YAAI,WAAU;sBAoBb,cAAA,uVAAC;gBAAI,WAAU;0BAEb,cAAA,uVAAC,4IAAA,CAAA,kBAAe;oBACd,WAAW;oBACX,YAAY,WAAW;oBACvB,YAAY,CAAC;oBACb,gBAAgB,CAAA,GAAA,mHAAA,CAAA,8BAA2B,AAAD,EAAE;8BAE5C,cAAA,uVAAC,0HAAA,CAAA,QAAK;wBACJ,SAAS,kBAAkB,yBAAyB,cAAc;wBAClE,WAAW,CAAC,+BAA+B,EAAE,CAAC,yBAAyB,kCAAkC,kBAAkB;wBAC3H,SAAS;4BACP,IAAI,wBAAwB;gCAC1B,kBAAkB,CAAC;4BACrB;wBACF;;0CAEA,uVAAC,4RAAA,CAAA,YAAS;gCAAC,WAAW,CAAC,mBAAmB,EACxC,kBAAkB,yBAAyB,eAAe,iBAC1D;;;;;;0CACF,uVAAC;gCAAK,WAAU;0CAAgB;;;;;;4BAC/B,kBAAkB,0BAA0B,WAAW,6BACtD,uVAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;cAwB7B,CAAC,WAAW;IACV,IAAI,UAAU,MAAM,KAAK,UAAU,MAAM,EAAE,OAAO;IAClD,IAAI,UAAU,cAAc,KAAK,UAAU,cAAc,EAAE,OAAO;IAClE,IAAI,UAAU,qBAAqB,KAAK,UAAU,qBAAqB,EAAE,OAAO;IAChF,IAAI,UAAU,sBAAsB,KAAK,UAAU,sBAAsB,EAAE,OAAO;IAClF,IAAI,UAAU,eAAe,KAAK,UAAU,eAAe,EAAE,OAAO;IACpE,OAAO;AACT;AAGF,cAAc,WAAW,GAAG;AAErB,MAAM,6BAAe,CAAA,GAAA,8SAAA,CAAA,OAAI,AAAD,EAC7B,kBACA,CAAC,WAAW;IACV,IAAI,UAAU,KAAK,KAAK,UAAU,KAAK,EAAE,OAAO;IAChD,IAAI,UAAU,MAAM,KAAK,UAAU,MAAM,EAAE,OAAO;IAClD,IAAI,CAAC,CAAA,GAAA,gNAAA,CAAA,UAAK,AAAD,EAAE,UAAU,WAAW,EAAE,UAAU,WAAW,GAAG,OAAO;IACjE,IAAI,UAAU,cAAc,KAAK,UAAU,cAAc,EAAE,OAAO;IAClE,IAAI,UAAU,qBAAqB,KAAK,UAAU,qBAAqB,EAAE,OAAO;IAChF,IAAI,UAAU,sBAAsB,KAAK,UAAU,sBAAsB,EAAE,OAAO;IAClF,IAAI,UAAU,eAAe,KAAK,UAAU,eAAe,EAAE,OAAO;IACpE,OAAO;AACT,IAEF,+CAA+C;CAE/C,gDAAgD;CAEhD,6CAA6C", "debugId": null}}, {"offset": {"line": 1089, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/chat-map/overview.tsx"], "sourcesContent": ["import { motion } from 'framer-motion';\r\nimport { BotIcon, EarthIcon, MapIcon, LayersIcon, NavigationIcon, ImageIcon, ChevronRightIcon, SparklesIcon, SearchIcon, FilterIcon, SettingsIcon, Layers3Icon, BarChart3Icon, MapPinIcon } from 'lucide-react';\r\nimport { Card, CardContent } from \"@/components/ui/card\";\r\nimport {\r\n  Accordion,\r\n  AccordionContent,\r\n  AccordionItem,\r\n  AccordionTrigger,\r\n} from \"@/components/ui/accordion\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from \"@/components/ui/popover\";\r\n\r\ninterface ExampleCategory {\r\n  title: string;\r\n  icon: any;\r\n  examples: Array<{\r\n    label: string;\r\n    command: string;\r\n  }>;\r\n}\r\n\r\ninterface OverviewProps {\r\n  setInput: (input: string) => void;\r\n}\r\n\r\nexport const Overview = ({ setInput }: OverviewProps) => {\r\n  const examples: ExampleCategory[] = [\r\n    {\r\n      title: \"장소 검색 및 이동\",\r\n      icon: <MapPinIcon className=\"w-4 h-4 text-emerald-600\" />,\r\n      examples: [\r\n        { label: \"위치 이동\", command: \"웨이버스로 이동해줘\" },\r\n        { label: \"길찾기\", command: \"웨이버스에서 평촌역까지 얼마나 걸려?\" },\r\n        { label: \"현재위치에서 길찾기\", command: \"내 위치에서 구로디지털단지역까지 얼마나걸려?\" },\r\n        // { label: \"내 위치\", command: \"내 위치로 이동해줘\" },\r\n      ]\r\n    },\r\n    {\r\n      title: \"지도 제어\",\r\n      icon: <SettingsIcon className=\"w-4 h-4 text-orange-600\" />,\r\n      examples: [\r\n        { label: \"지도 확대\", command: \"지도를 확대해줘\" },\r\n        { label: \"지도 축소\", command: \"지도를 축소해줘\" },\r\n        { label: \"위쪽으로 500m 이동\", command: \"위쪽으로 500m 이동해줘\" },\r\n        { label: \"항공지도로 변경\", command: \"배경지도를 항공지도로 변경해줘\" }\r\n      ]\r\n    },\r\n    {\r\n      title: \"레이어 제어\",\r\n      icon: <Layers3Icon className=\"w-4 h-4 text-purple-600\" />,\r\n      examples: [\r\n        { label: \"레이어 추가\", command: \"택지개발사업 레이어를 추가해줘\" },\r\n        { label: \"단일 스타일 설정\", command: \"백년가게를 노란색 별모양으로 보여줄래?\" },\r\n        { label: \"유형별 스타일 설정\", command: \"서울에 있는 약국만 빨간색으로 표시해줘\" },\r\n        { label: \"유형별 스타일 설정\", command: \"서울의 건물을 5층까지 노란색, 10층까지 파란색, 나머지는 빨간색으로 표시해줄래?\" },\r\n      ]\r\n    },\r\n    {\r\n      title: \"데이터 분석\",\r\n      icon: <BarChart3Icon className=\"w-4 h-4 text-rose-600\" />,\r\n      examples: [\r\n        { label: \"노후화 건물 분석\", command: \"서울의 노후화된 건물을 보여줘\" },\r\n        { label: \"레이어 밀도 분석\", command: \"AI 발생농가 지역의 밀집도를 분석해줘\" },\r\n      ]\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <motion.div\r\n      key=\"overview\"\r\n      initial={{ opacity: 0, y: 10 }}\r\n      animate={{ opacity: 1, y: 0 }}\r\n      exit={{ opacity: 0, y: -10 }}\r\n      transition={{ duration: 0.3 }}\r\n      className=\"w-full max-w-lg backdrop-blur-sm mx-auto my-auto bg-gradient-to-b from-background/10 to-background/80 rounded-xl\"\r\n    >\r\n      <Card className=\"border-none shadow-none bg-transparent\">\r\n        <CardContent className=\"p-6 space-y-6\">\r\n          {/* Header Section */}\r\n          <div className=\"relative\">\r\n            <motion.div\r\n              className=\"flex items-center justify-center gap-4\"\r\n              initial={{ scale: 0.9 }}\r\n              animate={{ scale: 1 }}\r\n              transition={{ delay: 0.2, type: \"spring\", stiffness: 150 }}\r\n            >\r\n              <div className=\"relative\">\r\n                <EarthIcon size={32} className=\"text-emerald-600\" />\r\n                <motion.div\r\n                  className=\"absolute -top-1 -right-1\"\r\n                  animate={{ rotate: 360 }}\r\n                  transition={{ duration: 20, repeat: Infinity, ease: \"linear\" }}\r\n                >\r\n                  <SparklesIcon size={16} className=\"text-yellow-500\" />\r\n                </motion.div>\r\n              </div>\r\n              <span className=\"font-bold text-3xl bg-gradient-to-r from-emerald-600 to-blue-600 bg-clip-text text-transparent\">×</span>\r\n              <BotIcon size={32} className=\"text-blue-600\" />\r\n            </motion.div>\r\n            <motion.div\r\n              className=\"text-center mt-4 space-y-2\"\r\n              initial={{ opacity: 0 }}\r\n              animate={{ opacity: 1 }}\r\n              transition={{ delay: 0.3 }}\r\n            >\r\n              <h2 className=\"text-xl font-bold bg-gradient-to-r from-emerald-600 via-blue-600 to-purple-600 bg-clip-text text-transparent\">\r\n                말로 만드는 지도\r\n              </h2>\r\n              <p className=\"text-sm text-muted-foreground\">\r\n                자연어로 지도를 제어해보세요.\r\n              </p>\r\n            </motion.div>\r\n          </div>\r\n\r\n          {/* Main Content */}\r\n          <motion.div\r\n            initial={{ opacity: 0 }}\r\n            animate={{ opacity: 1 }}\r\n            transition={{ delay: 0.4 }}\r\n            className=\"space-y-4\"\r\n          >\r\n\r\n\r\n            <Accordion type=\"single\" collapsible className=\"w-full bg-background/40 rounded-lg border border-border/20\">\r\n              <AccordionItem value=\"examples\" className=\"border-none\">\r\n                <AccordionTrigger className=\"justify-center gap-2 py-3 hover:no-underline\">\r\n                  <span className=\"text-sm font-medium\">✨ 기능 살펴보기</span>\r\n                </AccordionTrigger>\r\n                <AccordionContent className=\"flex py-4 justify-center pr-5\">\r\n                  <motion.div\r\n                    className=\"space-y-4\"\r\n                    variants={{\r\n                      hidden: { opacity: 0 },\r\n                      show: {\r\n                        opacity: 1,\r\n                        transition: {\r\n                          staggerChildren: 0.1\r\n                        }\r\n                      }\r\n                    }}\r\n                    initial=\"hidden\"\r\n                    animate=\"show\"\r\n                  >\r\n                    {examples.map((category, idx) => (\r\n                      <motion.div\r\n                        key={idx}\r\n                        variants={{\r\n                          hidden: { opacity: 0, x: -10 },\r\n                          show: { opacity: 1, x: 0 }\r\n                        }}\r\n                        className=\"space-y-2\"\r\n                      >\r\n                        <Popover>\r\n                          <PopoverTrigger asChild>\r\n                            <Button\r\n                              variant=\"ghost\"\r\n                              className=\"w-full justify-start gap-2 hover:bg-primary/10\"\r\n                            >\r\n                              {category.icon}\r\n                              <span>{category.title}</span>\r\n                            </Button>\r\n                          </PopoverTrigger>\r\n                          <PopoverContent className=\"w-80 p-4 bg-gradient-to-b from-background/95 to-background/98 backdrop-blur-lg border border-border/40 shadow-lg animate-in fade-in-0 zoom-in-95 duration-200\">\r\n                            <div className=\"space-y-4\">\r\n                              {/* 헤더 섹션 */}\r\n                              <div className=\"border-b border-border/40 pb-3\">\r\n                                <div className=\"flex items-center gap-2 mb-2\">\r\n                                  {category.icon}\r\n                                  <h4 className=\"font-semibold text-sm text-foreground\">\r\n                                    {category.title}\r\n                                  </h4>\r\n                                </div>\r\n                                <p className=\"text-xs text-muted-foreground/80\">아래 예시 중에서 선택해보세요</p>\r\n                              </div>\r\n\r\n                              {/* 예시 목록 */}\r\n                              <div className=\"space-y-2\">\r\n                                {category.examples.map((example, index) => (\r\n                                  <Button\r\n                                    key={index}\r\n                                    variant=\"ghost\"\r\n                                    className=\"w-full group justify-between relative overflow-hidden rounded-lg px-0\"\r\n                                    onClick={() => setInput(example.command)}\r\n                                  >\r\n                                    <div className=\"absolute inset-0 bg-gradient-to-r from-primary/5 to-primary/10 opacity-0 group-hover:opacity-100 transition-opacity duration-200\" />\r\n                                    <div className=\"relative flex flex-col items-start gap-1 p-3 \">\r\n                                      <span className=\"font-medium text-sm text-foreground/90 group-hover:text-foreground transition-colors\">\r\n                                        {example.label}\r\n                                      </span>\r\n                                      <span className=\"text-xs text-muted-foreground/70 group-hover:text-muted-foreground transition-colors\">\r\n                                        {example.command}\r\n                                      </span>\r\n                                    </div>\r\n                                    <div className=\"absolute right-3 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200\">\r\n                                      <ChevronRightIcon className=\"w-4 h-4 text-muted-foreground\" />\r\n                                    </div>\r\n                                  </Button>\r\n                                ))}\r\n                              </div>\r\n                            </div>\r\n                          </PopoverContent>\r\n                        </Popover>\r\n                      </motion.div>\r\n                    ))}\r\n                  </motion.div>\r\n                </AccordionContent>\r\n              </AccordionItem>\r\n            </Accordion>\r\n          </motion.div>\r\n\r\n          {/* Footer */}\r\n          <motion.p\r\n            className=\"text-xs text-center text-muted-foreground\"\r\n            initial={{ opacity: 0 }}\r\n            animate={{ opacity: 1 }}\r\n            transition={{ delay: 0.5 }}\r\n          >\r\n            각 예시를 클릭하면 자동으로 입력됩니다\r\n          </motion.p>\r\n        </CardContent>\r\n      </Card>\r\n    </motion.div>\r\n  );\r\n};\r\n\r\nexport default Overview;"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAMA;AACA;;;;;;;;AAmBO,MAAM,WAAW,CAAC,EAAE,QAAQ,EAAiB;IAClD,MAAM,WAA8B;QAClC;YACE,OAAO;YACP,oBAAM,uVAAC,kSAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;YAC5B,UAAU;gBACR;oBAAE,OAAO;oBAAS,SAAS;gBAAa;gBACxC;oBAAE,OAAO;oBAAO,SAAS;gBAAuB;gBAChD;oBAAE,OAAO;oBAAc,SAAS;gBAA2B;aAE5D;QACH;QACA;YACE,OAAO;YACP,oBAAM,uVAAC,kSAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;YAC9B,UAAU;gBACR;oBAAE,OAAO;oBAAS,SAAS;gBAAW;gBACtC;oBAAE,OAAO;oBAAS,SAAS;gBAAW;gBACtC;oBAAE,OAAO;oBAAgB,SAAS;gBAAiB;gBACnD;oBAAE,OAAO;oBAAY,SAAS;gBAAmB;aAClD;QACH;QACA;YACE,OAAO;YACP,oBAAM,uVAAC,oSAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;YAC7B,UAAU;gBACR;oBAAE,OAAO;oBAAU,SAAS;gBAAmB;gBAC/C;oBAAE,OAAO;oBAAa,SAAS;gBAAwB;gBACvD;oBAAE,OAAO;oBAAc,SAAS;gBAAwB;gBACxD;oBAAE,OAAO;oBAAc,SAAS;gBAAiD;aAClF;QACH;QACA;YACE,OAAO;YACP,oBAAM,uVAAC,4SAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;YAC/B,UAAU;gBACR;oBAAE,OAAO;oBAAa,SAAS;gBAAmB;gBAClD;oBAAE,OAAO;oBAAa,SAAS;gBAAwB;aACxD;QACH;KACD;IAED,qBACE,uVAAC,gSAAA,CAAA,SAAM,CAAC,GAAG;QAET,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,MAAM;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC3B,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAU;kBAEV,cAAA,uVAAC,yHAAA,CAAA,OAAI;YAAC,WAAU;sBACd,cAAA,uVAAC,yHAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,uVAAC;wBAAI,WAAU;;0CACb,uVAAC,gSAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,OAAO;gCAAI;gCACtB,SAAS;oCAAE,OAAO;gCAAE;gCACpB,YAAY;oCAAE,OAAO;oCAAK,MAAM;oCAAU,WAAW;gCAAI;;kDAEzD,uVAAC;wCAAI,WAAU;;0DACb,uVAAC,4RAAA,CAAA,YAAS;gDAAC,MAAM;gDAAI,WAAU;;;;;;0DAC/B,uVAAC,gSAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDAAE,QAAQ;gDAAI;gDACvB,YAAY;oDAAE,UAAU;oDAAI,QAAQ;oDAAU,MAAM;gDAAS;0DAE7D,cAAA,uVAAC,kSAAA,CAAA,eAAY;oDAAC,MAAM;oDAAI,WAAU;;;;;;;;;;;;;;;;;kDAGtC,uVAAC;wCAAK,WAAU;kDAAiG;;;;;;kDACjH,uVAAC,wRAAA,CAAA,UAAO;wCAAC,MAAM;wCAAI,WAAU;;;;;;;;;;;;0CAE/B,uVAAC,gSAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,SAAS;gCAAE;gCACtB,SAAS;oCAAE,SAAS;gCAAE;gCACtB,YAAY;oCAAE,OAAO;gCAAI;;kDAEzB,uVAAC;wCAAG,WAAU;kDAA+G;;;;;;kDAG7H,uVAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;kCAOjD,uVAAC,gSAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,YAAY;4BAAE,OAAO;wBAAI;wBACzB,WAAU;kCAIV,cAAA,uVAAC,8HAAA,CAAA,YAAS;4BAAC,MAAK;4BAAS,WAAW;4BAAC,WAAU;sCAC7C,cAAA,uVAAC,8HAAA,CAAA,gBAAa;gCAAC,OAAM;gCAAW,WAAU;;kDACxC,uVAAC,8HAAA,CAAA,mBAAgB;wCAAC,WAAU;kDAC1B,cAAA,uVAAC;4CAAK,WAAU;sDAAsB;;;;;;;;;;;kDAExC,uVAAC,8HAAA,CAAA,mBAAgB;wCAAC,WAAU;kDAC1B,cAAA,uVAAC,gSAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,UAAU;gDACR,QAAQ;oDAAE,SAAS;gDAAE;gDACrB,MAAM;oDACJ,SAAS;oDACT,YAAY;wDACV,iBAAiB;oDACnB;gDACF;4CACF;4CACA,SAAQ;4CACR,SAAQ;sDAEP,SAAS,GAAG,CAAC,CAAC,UAAU,oBACvB,uVAAC,gSAAA,CAAA,SAAM,CAAC,GAAG;oDAET,UAAU;wDACR,QAAQ;4DAAE,SAAS;4DAAG,GAAG,CAAC;wDAAG;wDAC7B,MAAM;4DAAE,SAAS;4DAAG,GAAG;wDAAE;oDAC3B;oDACA,WAAU;8DAEV,cAAA,uVAAC,4HAAA,CAAA,UAAO;;0EACN,uVAAC,4HAAA,CAAA,iBAAc;gEAAC,OAAO;0EACrB,cAAA,uVAAC,2HAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,WAAU;;wEAET,SAAS,IAAI;sFACd,uVAAC;sFAAM,SAAS,KAAK;;;;;;;;;;;;;;;;;0EAGzB,uVAAC,4HAAA,CAAA,iBAAc;gEAAC,WAAU;0EACxB,cAAA,uVAAC;oEAAI,WAAU;;sFAEb,uVAAC;4EAAI,WAAU;;8FACb,uVAAC;oFAAI,WAAU;;wFACZ,SAAS,IAAI;sGACd,uVAAC;4FAAG,WAAU;sGACX,SAAS,KAAK;;;;;;;;;;;;8FAGnB,uVAAC;oFAAE,WAAU;8FAAmC;;;;;;;;;;;;sFAIlD,uVAAC;4EAAI,WAAU;sFACZ,SAAS,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC/B,uVAAC,2HAAA,CAAA,SAAM;oFAEL,SAAQ;oFACR,WAAU;oFACV,SAAS,IAAM,SAAS,QAAQ,OAAO;;sGAEvC,uVAAC;4FAAI,WAAU;;;;;;sGACf,uVAAC;4FAAI,WAAU;;8GACb,uVAAC;oGAAK,WAAU;8GACb,QAAQ,KAAK;;;;;;8GAEhB,uVAAC;oGAAK,WAAU;8GACb,QAAQ,OAAO;;;;;;;;;;;;sGAGpB,uVAAC;4FAAI,WAAU;sGACb,cAAA,uVAAC,8SAAA,CAAA,mBAAgB;gGAAC,WAAU;;;;;;;;;;;;mFAfzB;;;;;;;;;;;;;;;;;;;;;;;;;;;mDAlCZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAkEnB,uVAAC,gSAAA,CAAA,SAAM,CAAC,CAAC;wBACP,WAAU;wBACV,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,YAAY;4BAAE,OAAO;wBAAI;kCAC1B;;;;;;;;;;;;;;;;;OAnJD;;;;;AA0JV;uCAEe", "debugId": null}}, {"offset": {"line": 1641, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/chat-map/help-menu-bar.tsx"], "sourcesContent": ["import React, { useState } from \"react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport {\n  Popover,\n  PopoverContent,\n  PopoverTrigger,\n} from \"@/components/ui/popover\";\nimport {\n  Tooltip,\n  TooltipContent,\n  TooltipTrigger,\n} from \"@/components/ui/tooltip\";\nimport {\n  SettingsIcon,\n  MessageSquarePlus,\n  MapPin,\n  Layers3,\n  BarChart3,\n  Sparkles,\n  ArrowRight,\n  BadgeHelp,\n} from \"lucide-react\";\nimport { useRouter } from \"next/navigation\";\nimport { MagicCard } from \"@/components/magicui/magic-card\";\nimport { cn } from \"@/lib/utils\";\n\ninterface HelpMenuBarProps {\n  setInput: (input: string) => void;\n  hasMessages: boolean;\n}\n\nexport const HelpMenuBar = ({ setInput }: HelpMenuBarProps) => {\n  const [activePopover, setActivePopover] = useState<string | null>(null);\n  const router = useRouter();\n\n  const menuItems = [\n    {\n      id: \"help\",\n      icon: <BadgeHelp className=\"w-5 h-5\" />,\n      title: \"도움말\",\n      subtitle: \"기능 안내\",\n      tooltip: \"챗봇의 모든 기능을 한눈에 확인하세요\",\n      color: \"text-blue-600\",\n      bgColor: \"hover:bg-blue-50\",\n      gradient: \"from-blue-500/10 to-blue-500/10\",\n      features: [\n        {\n          icon: \"🗺️\",\n          title: \"장소 검색 및 이동\",\n          description: \"주소검색, 현재위치확인, 길찾기\",\n          category: \"navigation\"\n        },\n        {\n          icon: \"⚙️\",\n          title: \"지도 제어\",\n          description: \"상하좌우 이동, 줌레벨 변경, 배경지도 변경\",\n          category: \"control\"\n        },\n        {\n          icon: \"📚\",\n          title: \"레이어 제어\",\n          description: \"레이어 목록 조회, 상세 조회, 속성정보 조회, 스타일 변경, 레이어 제거\",\n          category: \"layers\"\n        },\n        {\n          icon: \"📊\",\n          title: \"데이터 분석\",\n          description: \"밀도 분석, 속성 필터\",\n          category: \"analysis\"\n        }\n      ]\n    },\n    {\n      id: \"search\",\n      icon: <MapPin className=\"w-5 h-5\" />,\n      title: \"장소 검색\",\n      subtitle: \"위치 찾기 & 길찾기\",\n      tooltip: \"원하는 장소로 빠르게 이동하세요\",\n      color: \"text-emerald-600\",\n      bgColor: \"hover:bg-emerald-50\",\n      gradient: \"from-emerald-500/10 to-green-500/10\",\n      tools: [\"주소검색\", \"현재위치확인\", \"길찾기\"],\n      examples: [\n        { label: \"위치 이동\", command: \"웨이버스로 이동해줘\", type: \"secondary\" },\n        { label: \"길찾기\", command: \"웨이버스에서 평촌역까지 얼마나 걸려?\", type: \"secondary\" },\n        { label: \"현재위치 기반\", command: \"내 위치에서 구로디지털단지역까지 얼마나걸려?\", type: \"secondary\" },\n      ]\n    },\n    {\n      id: \"control\",\n      icon: <SettingsIcon className=\"w-5 h-5\" />,\n      title: \"지도 제어\",\n      subtitle: \"뷰포트 & 배경지도\",\n      tooltip: \"지도 화면을 자유자재로 조작하세요\",\n      color: \"text-orange-600\",\n      bgColor: \"hover:bg-orange-50\",\n      gradient: \"from-orange-500/10 to-amber-500/10\",\n      tools: [\"상하좌우 이동\", \"줌레벨 변경\", \"배경지도 변경\"],\n      examples: [\n        { label: \"지도 확대\", command: \"지도를 확대해줘\", type: \"secondary\" },\n        { label: \"지도 축소\", command: \"지도를 축소해줘\", type: \"secondary\" },\n        { label: \"방향 이동\", command: \"위쪽으로 500m 이동해줘\", type: \"secondary\" },\n        { label: \"배경지도 변경\", command: \"배경지도를 항공지도로 변경해줘\", type: \"secondary\" }\n      ]\n    },\n    {\n      id: \"layers\",\n      icon: <Layers3 className=\"w-5 h-5\" />,\n      title: \"레이어 제어\",\n      subtitle: \"데이터 시각화 & 스타일링\",\n      tooltip: \"다양한 데이터 레이어를 관리하고 꾸며보세요\",\n      color: \"text-purple-600\",\n      bgColor: \"hover:bg-purple-50\",\n      gradient: \"from-purple-500/10 to-violet-500/10\",\n      tools: [\"레이어 목록 조회\", \"상세 조회\", \"속성정보 조회\", \"스타일 변경\", \"유형별 스타일 변경\", \"레이어 제거\"],\n      examples: [\n        { label: \"레이어 추가\", command: \"택지개발사업 레이어를 추가해줘\", type: \"secondary\" },\n        { label: \"단일 스타일\", command: \"백년가게를 노란색 별모양으로 보여줄래?\", type: \"secondary\" },\n        { label: \"조건부 스타일\", command: \"서울에 있는 약국만 빨간색으로 표시해줘\", type: \"secondary\" },\n        { label: \"유형별 스타일\", command: \"서울의 건물을 5층까지 노란색, 10층까지 파란색, 나머지는 빨간색으로 표시해줄래?\", type: \"advanced\" },\n      ]\n    },\n    {\n      id: \"analysis\",\n      icon: <BarChart3 className=\"w-5 h-5\" />,\n      title: \"데이터 분석\",\n      subtitle: \"공간 분석 & 필터링\",\n      tooltip: \"데이터에서 의미있는 패턴을 발견하세요\",\n      color: \"text-rose-600\",\n      bgColor: \"hover:bg-rose-50\",\n      gradient: \"from-rose-500/10 to-pink-500/10\",\n      tools: [\"밀도 분석\", \"속성 필터\"],\n      examples: [\n        { label: \"노후화 분석\", command: \"서울의 노후화된 건물을 보여줘\", type: \"advanced\" },\n        { label: \"밀도 분석\", command: \"AI 발생농가 지역의 밀집도를 분석해줘\", type: \"advanced\" },\n      ]\n    },\n  ];\n\n  const handleExampleClick = (command: string) => {\n    setInput(command);\n    setActivePopover(null);\n  };\n\n  const handleNewChat = () => {\n    // 새 대화 시작 로직 (현재는 페이지 새로고침으로 구현)\n    router.push('/');\n    router.refresh();\n  };\n\n  // 메시지가 없을 때도 표시 (Overview와 함께 표시)\n  return (\n    <div className=\"flex items-center justify-between px-4 py-2 border-b border-border/60 bg-gradient-to-r from-secondary/30 via-background/50 to-secondary/20 backdrop-blur-sm\">\n      {/* 왼쪽: 기능 아이콘들 */}\n      <div className=\"flex items-center gap-3\">\n        {menuItems.map((item) => (\n          <Popover\n            key={item.id}\n            open={activePopover === item.id}\n            onOpenChange={(open) => setActivePopover(open ? item.id : null)}\n          >\n            <Tooltip>\n              <TooltipTrigger asChild>\n                <PopoverTrigger asChild>\n                  <Button\n                    variant=\"secondary\"\n                    size=\"sm\"\n                    className={cn(\n                      \"h-10 w-10 p-0 transition-all duration-300 border rounded-xl relative overflow-hidden group\",\n                      \"hover:shadow-md hover:scale-105 active:scale-95\",\n                      activePopover === item.id\n                        ? `${item.color} bg-gradient-to-br ${item.gradient} border-current/30 shadow-lg scale-105`\n                        : `${item.color} bg-background/80 border-border/40 hover:border-current/40 hover:bg-gradient-to-br hover:${item.gradient}`\n                    )}\n                  >\n                    <div className=\"absolute inset-0 bg-gradient-to-br from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\" />\n                    <div className=\"relative\">\n                      {item.icon}\n                    </div>\n                  </Button>\n                </PopoverTrigger>\n              </TooltipTrigger>\n              <TooltipContent side=\"bottom\" className=\"text-xs font-medium bg-background/95 backdrop-blur-sm border border-border/40\">\n                <div className=\"flex flex-col items-center gap-1\">\n                  <span className=\"font-semibold\">{item.title}</span>\n                  <span className=\"text-muted-foreground text-xs\">{item.subtitle}</span>\n                </div>\n              </TooltipContent>\n            </Tooltip>\n            <PopoverContent\n              className={cn(\n                \"p-0 border shadow-xl backdrop-blur-xl\",\n                item.id === \"help\"\n                  ? \"w-[420px] border-slate-200/30 bg-gradient-to-br from-background/95 via-background/98 to-slate-50/20\"\n                  : \"w-96 border-border/30 bg-gradient-to-br from-background/95 via-background/98 to-background/90\"\n              )}\n              align=\"start\"\n              side=\"bottom\"\n              sideOffset={12}\n            >\n              {/* 도움말 아이콘인 경우 MagicCard로 래핑 */}\n              {item.id === \"help\" ? (\n                <MagicCard\n                  className=\"p-5 bg-gradient-to-br from-background via-background/95 to-background/90\"\n                  gradientSize={100}\n                  gradientColor={\"#64748b\"}\n                  gradientOpacity={0.04}\n                  gradientFrom={\"#64748b\"}\n                  gradientTo={\"#6b7280\"}\n                >\n                  {/* 헤더 */}\n                  <div className=\"flex items-center gap-3 mb-5\">\n                    <div className={cn(\n                      \"flex items-center justify-center w-10 h-10 rounded-xl border\",\n                      `bg-gradient-to-br ${item.gradient} border-current/20`,\n                      item.color\n                    )}>\n                      {item.icon}\n                    </div>\n                    <div>\n                      <h4 className=\"font-bold text-base text-foreground\">\n                        {item.title}\n                      </h4>\n                      <p className=\"text-xs text-muted-foreground\">\n                        {item.subtitle}\n                      </p>\n                    </div>\n                  </div>\n                  {/* 기능 안내 */}\n                  {item.features && (\n                    <div className=\"space-y-4\">\n                      <div className=\"bg-gradient-to-br from-primary/5 via-primary/2 to-primary/5 rounded-lg p-4 border border-primary/8\">\n                        <h5 className=\"font-semibold text-sm text-foreground mb-3 flex items-center gap-2\">\n                          <div className=\"w-2 h-2 bg-gradient-to-r from-primary to-primary/10 rounded-full\"></div>\n                          지원 기능\n                        </h5>\n                        <div className=\"grid gap-3\">\n                          {item.features.map((feature, index) => {\n                            const IconComponent = {\n                              navigation: MapPin,\n                              control: SettingsIcon,\n                              layers: Layers3,\n                              analysis: BarChart3\n                            }[feature.category];\n\n                            const categoryColors: Record<string, string> = {\n                              navigation: \"text-emerald-600\",\n                              control: \"text-orange-600\",\n                              layers: \"text-purple-600\",\n                              analysis: \"text-rose-600\"\n                            };\n\n                            const categoryGradients: Record<string, string> = {\n                              navigation: \"from-emerald-500/10 to-green-500/10\",\n                              control: \"from-orange-500/10 to-amber-500/10\",\n                              layers: \"from-purple-500/10 to-violet-500/10\",\n                              analysis: \"from-rose-500/10 to-pink-500/10\"\n                            };\n\n                            return (\n                              <div key={index} className=\"group\">\n                                <div className=\"flex items-center gap-3 p-3 bg-background/50 backdrop-blur-sm rounded-md border border-border/20 hover:border-primary/15 transition-all duration-200 hover:shadow-sm\">\n                                  <div className={cn(\n                                    \"flex items-center justify-center w-8 h-8 rounded-md border\",\n                                    `bg-gradient-to-br ${categoryGradients[feature.category]} border-current/20`,\n                                    categoryColors[feature.category]\n                                  )}>\n                                    {IconComponent ? (\n                                      <IconComponent className=\"w-4 h-4\" />\n                                    ) : (\n                                      <span className=\"text-lg\">{feature.icon}</span>\n                                    )}\n                                  </div>\n                                  <div className=\"flex-1 min-w-0\">\n                                    <h6 className=\"font-medium text-xs text-foreground mb-0.5\">\n                                      {feature.title}\n                                    </h6>\n                                    <p className=\"text-xs text-muted-foreground leading-tight\">\n                                      {feature.description}\n                                    </p>\n                                  </div>\n                                </div>\n                              </div>\n                            );\n                          })}\n                        </div>\n                      </div>\n\n                      <div className=\"text-center pt-1 border-t border-border/15\">\n                        <p className=\"text-xs text-muted-foreground/80\">\n                          💬 자연어로 명령해보세요. 예: \"강남역으로 가줘\", \"지도를 확대해줘\"\n                        </p>\n                      </div>\n                    </div>\n                  )}\n                </MagicCard>\n              ) : (\n                /* 일반 기능 아이콘인 경우 개선된 스타일 */\n                <div className=\"p-5 space-y-4\">\n                  {/* 헤더 */}\n                  <div className=\"border-b border-border/30 pb-4\">\n                    <div className=\"flex items-center gap-3 mb-3\">\n                      <div className={cn(\n                        \"flex items-center justify-center w-10 h-10 rounded-xl border\",\n                        `bg-gradient-to-br ${item.gradient} border-current/20`,\n                        item.color\n                      )}>\n                        {item.icon}\n                      </div>\n                      <div>\n                        <h4 className=\"font-bold text-base text-foreground\">\n                          {item.title}\n                        </h4>\n                        <p className=\"text-xs text-muted-foreground\">\n                          {item.subtitle}\n                        </p>\n                      </div>\n                    </div>\n\n                    {item.tools && (\n                      <div className=\"bg-secondary/30 rounded-lg p-3 mb-3\">\n                        <p className=\"text-xs font-medium text-foreground/90 mb-2 flex items-center gap-2\">\n                          <span className=\"w-1.5 h-1.5 bg-primary rounded-full\"></span>\n                          지원 도구\n                        </p>\n                        <div className=\"flex flex-wrap gap-1\">\n                          {item.tools.map((tool, toolIndex) => (\n                            <span key={toolIndex} className=\"inline-flex items-center px-2 py-1 rounded-md bg-background/60 border border-border/40 text-xs text-foreground/80\">\n                              {tool}\n                            </span>\n                          ))}\n                        </div>\n                      </div>\n                    )}\n                    <p className=\"text-xs text-muted-foreground/80 flex items-center gap-2\">\n                      <span className=\"w-1 h-1 bg-primary/60 rounded-full\"></span>\n                      예시를 클릭하면 자동으로 입력됩니다\n                    </p>\n                  </div>\n\n                  {/* 예시 목록 */}\n                  {item.examples && (\n                    <div className=\"space-y-2 max-h-72 overflow-y-auto styled-scrollbar\">\n                      {item.examples.map((example, exampleIndex) => (\n                        <Button\n                          key={exampleIndex}\n                          variant=\"ghost\"\n                          className={cn(\n                            \"w-full group justify-between relative overflow-hidden rounded-lg p-2 h-auto border border-transparent\",\n                            \"hover:border-primary/20 hover:shadow-sm transition-all duration-300\"\n                          )}\n                          onClick={() => handleExampleClick(example.command)}\n                        >\n                          <div className={cn(\n                            \"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                            `bg-gradient-to-r ${item.gradient}`\n                          )} />\n                          <div className=\"relative flex items-center gap-3 text-left flex-1\">\n                            <div className={cn(\n                              \"flex items-center justify-center w-6 h-6 rounded-md text-xs\",\n                              example.type === 'primary' ? 'bg-primary/10 text-primary' :\n                                example.type === 'advanced' ? 'bg-purple/10 text-purple-600' :\n                                  'bg-secondary/50 text-muted-foreground'\n                            )}>\n                              {example.type === 'primary' ? '★' :\n                                example.type === 'advanced' ? '◆' : '•'}\n                            </div>\n                            <div className=\"flex flex-col gap-1 flex-1 min-w-0\">\n                              <span className=\"text-sm font-medium text-foreground/90 group-hover:text-primary transition-colors\">\n                                {example.label}\n                              </span>\n                              <span className=\"text-xs text-muted-foreground/70 group-hover:text-muted-foreground transition-colors line-clamp-2\">\n                                {example.command}\n                              </span>\n                            </div>\n                          </div>\n                          <div className=\"relative opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\n                            <ArrowRight className=\"w-4 h-4 text-primary\" />\n                          </div>\n                        </Button>\n                      ))}\n                    </div>\n                  )}\n                </div>\n              )}\n            </PopoverContent>\n          </Popover>\n        ))}\n      </div>\n\n      {/* 오른쪽: 새 대화 버튼 */}\n      <Tooltip>\n        <TooltipTrigger asChild>\n          <Button\n            variant=\"secondary\"\n            size=\"sm\"\n            className={cn(\n              \"h-10 w-10 p-0 transition-all duration-300 border rounded-xl relative overflow-hidden group\",\n              \"bg-background/80 border-border/40 hover:border-primary/40 hover:shadow-md hover:scale-105 active:scale-95\",\n              \"text-muted-foreground hover:text-primary hover:bg-gradient-to-br hover:from-primary/5 hover:to-primary/10\"\n            )}\n            onClick={handleNewChat}\n          >\n            <div className=\"absolute inset-0 bg-gradient-to-br from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\" />\n            <div className=\"relative\">\n              <MessageSquarePlus className=\"w-5 h-5\" />\n            </div>\n          </Button>\n        </TooltipTrigger>\n        <TooltipContent side=\"bottom\" className=\"text-xs font-medium bg-background/95 backdrop-blur-sm border border-border/40\">\n          <div className=\"flex flex-col items-center gap-1\">\n            <span className=\"font-semibold\">새 대화</span>\n            <span className=\"text-muted-foreground text-xs\">대화 초기화</span>\n          </div>\n        </TooltipContent>\n      </Tooltip>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAKA;AAKA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AACA;;;;;;;;;;AAOO,MAAM,cAAc,CAAC,EAAE,QAAQ,EAAoB;IACxD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,SAAS,CAAA,GAAA,2OAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,YAAY;QAChB;YACE,IAAI;YACJ,oBAAM,uVAAC,oSAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAC3B,OAAO;YACP,UAAU;YACV,SAAS;YACT,OAAO;YACP,SAAS;YACT,UAAU;YACV,UAAU;gBACR;oBACE,MAAM;oBACN,OAAO;oBACP,aAAa;oBACb,UAAU;gBACZ;gBACA;oBACE,MAAM;oBACN,OAAO;oBACP,aAAa;oBACb,UAAU;gBACZ;gBACA;oBACE,MAAM;oBACN,OAAO;oBACP,aAAa;oBACb,UAAU;gBACZ;gBACA;oBACE,MAAM;oBACN,OAAO;oBACP,aAAa;oBACb,UAAU;gBACZ;aACD;QACH;QACA;YACE,IAAI;YACJ,oBAAM,uVAAC,8RAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,OAAO;YACP,UAAU;YACV,SAAS;YACT,OAAO;YACP,SAAS;YACT,UAAU;YACV,OAAO;gBAAC;gBAAQ;gBAAU;aAAM;YAChC,UAAU;gBACR;oBAAE,OAAO;oBAAS,SAAS;oBAAc,MAAM;gBAAY;gBAC3D;oBAAE,OAAO;oBAAO,SAAS;oBAAwB,MAAM;gBAAY;gBACnE;oBAAE,OAAO;oBAAW,SAAS;oBAA4B,MAAM;gBAAY;aAC5E;QACH;QACA;YACE,IAAI;YACJ,oBAAM,uVAAC,kSAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;YAC9B,OAAO;YACP,UAAU;YACV,SAAS;YACT,OAAO;YACP,SAAS;YACT,UAAU;YACV,OAAO;gBAAC;gBAAW;gBAAU;aAAU;YACvC,UAAU;gBACR;oBAAE,OAAO;oBAAS,SAAS;oBAAY,MAAM;gBAAY;gBACzD;oBAAE,OAAO;oBAAS,SAAS;oBAAY,MAAM;gBAAY;gBACzD;oBAAE,OAAO;oBAAS,SAAS;oBAAkB,MAAM;gBAAY;gBAC/D;oBAAE,OAAO;oBAAW,SAAS;oBAAoB,MAAM;gBAAY;aACpE;QACH;QACA;YACE,IAAI;YACJ,oBAAM,uVAAC,gSAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;YACzB,OAAO;YACP,UAAU;YACV,SAAS;YACT,OAAO;YACP,SAAS;YACT,UAAU;YACV,OAAO;gBAAC;gBAAa;gBAAS;gBAAW;gBAAU;gBAAc;aAAS;YAC1E,UAAU;gBACR;oBAAE,OAAO;oBAAU,SAAS;oBAAoB,MAAM;gBAAY;gBAClE;oBAAE,OAAO;oBAAU,SAAS;oBAAyB,MAAM;gBAAY;gBACvE;oBAAE,OAAO;oBAAW,SAAS;oBAAyB,MAAM;gBAAY;gBACxE;oBAAE,OAAO;oBAAW,SAAS;oBAAkD,MAAM;gBAAW;aACjG;QACH;QACA;YACE,IAAI;YACJ,oBAAM,uVAAC,wSAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAC3B,OAAO;YACP,UAAU;YACV,SAAS;YACT,OAAO;YACP,SAAS;YACT,UAAU;YACV,OAAO;gBAAC;gBAAS;aAAQ;YACzB,UAAU;gBACR;oBAAE,OAAO;oBAAU,SAAS;oBAAoB,MAAM;gBAAW;gBACjE;oBAAE,OAAO;oBAAS,SAAS;oBAAyB,MAAM;gBAAW;aACtE;QACH;KACD;IAED,MAAM,qBAAqB,CAAC;QAC1B,SAAS;QACT,iBAAiB;IACnB;IAEA,MAAM,gBAAgB;QACpB,iCAAiC;QACjC,OAAO,IAAI,CAAC;QACZ,OAAO,OAAO;IAChB;IAEA,kCAAkC;IAClC,qBACE,uVAAC;QAAI,WAAU;;0BAEb,uVAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC,qBACd,uVAAC,4HAAA,CAAA,UAAO;wBAEN,MAAM,kBAAkB,KAAK,EAAE;wBAC/B,cAAc,CAAC,OAAS,iBAAiB,OAAO,KAAK,EAAE,GAAG;;0CAE1D,uVAAC,4HAAA,CAAA,UAAO;;kDACN,uVAAC,4HAAA,CAAA,iBAAc;wCAAC,OAAO;kDACrB,cAAA,uVAAC,4HAAA,CAAA,iBAAc;4CAAC,OAAO;sDACrB,cAAA,uVAAC,2HAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,8FACA,mDACA,kBAAkB,KAAK,EAAE,GACrB,GAAG,KAAK,KAAK,CAAC,mBAAmB,EAAE,KAAK,QAAQ,CAAC,sCAAsC,CAAC,GACxF,GAAG,KAAK,KAAK,CAAC,yFAAyF,EAAE,KAAK,QAAQ,EAAE;;kEAG9H,uVAAC;wDAAI,WAAU;;;;;;kEACf,uVAAC;wDAAI,WAAU;kEACZ,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;kDAKlB,uVAAC,4HAAA,CAAA,iBAAc;wCAAC,MAAK;wCAAS,WAAU;kDACtC,cAAA,uVAAC;4CAAI,WAAU;;8DACb,uVAAC;oDAAK,WAAU;8DAAiB,KAAK,KAAK;;;;;;8DAC3C,uVAAC;oDAAK,WAAU;8DAAiC,KAAK,QAAQ;;;;;;;;;;;;;;;;;;;;;;;0CAIpE,uVAAC,4HAAA,CAAA,iBAAc;gCACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,yCACA,KAAK,EAAE,KAAK,SACR,wGACA;gCAEN,OAAM;gCACN,MAAK;gCACL,YAAY;0CAGX,KAAK,EAAE,KAAK,uBACX,uVAAC,uIAAA,CAAA,YAAS;oCACR,WAAU;oCACV,cAAc;oCACd,eAAe;oCACf,iBAAiB;oCACjB,cAAc;oCACd,YAAY;;sDAGZ,uVAAC;4CAAI,WAAU;;8DACb,uVAAC;oDAAI,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACf,gEACA,CAAC,kBAAkB,EAAE,KAAK,QAAQ,CAAC,kBAAkB,CAAC,EACtD,KAAK,KAAK;8DAET,KAAK,IAAI;;;;;;8DAEZ,uVAAC;;sEACC,uVAAC;4DAAG,WAAU;sEACX,KAAK,KAAK;;;;;;sEAEb,uVAAC;4DAAE,WAAU;sEACV,KAAK,QAAQ;;;;;;;;;;;;;;;;;;wCAKnB,KAAK,QAAQ,kBACZ,uVAAC;4CAAI,WAAU;;8DACb,uVAAC;oDAAI,WAAU;;sEACb,uVAAC;4DAAG,WAAU;;8EACZ,uVAAC;oEAAI,WAAU;;;;;;gEAAyE;;;;;;;sEAG1F,uVAAC;4DAAI,WAAU;sEACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS;gEAC3B,MAAM,gBAAgB;oEACpB,YAAY,8RAAA,CAAA,SAAM;oEAClB,SAAS,kSAAA,CAAA,eAAY;oEACrB,QAAQ,gSAAA,CAAA,UAAO;oEACf,UAAU,wSAAA,CAAA,YAAS;gEACrB,CAAC,CAAC,QAAQ,QAAQ,CAAC;gEAEnB,MAAM,iBAAyC;oEAC7C,YAAY;oEACZ,SAAS;oEACT,QAAQ;oEACR,UAAU;gEACZ;gEAEA,MAAM,oBAA4C;oEAChD,YAAY;oEACZ,SAAS;oEACT,QAAQ;oEACR,UAAU;gEACZ;gEAEA,qBACE,uVAAC;oEAAgB,WAAU;8EACzB,cAAA,uVAAC;wEAAI,WAAU;;0FACb,uVAAC;gFAAI,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACf,8DACA,CAAC,kBAAkB,EAAE,iBAAiB,CAAC,QAAQ,QAAQ,CAAC,CAAC,kBAAkB,CAAC,EAC5E,cAAc,CAAC,QAAQ,QAAQ,CAAC;0FAE/B,8BACC,uVAAC;oFAAc,WAAU;;;;;yGAEzB,uVAAC;oFAAK,WAAU;8FAAW,QAAQ,IAAI;;;;;;;;;;;0FAG3C,uVAAC;gFAAI,WAAU;;kGACb,uVAAC;wFAAG,WAAU;kGACX,QAAQ,KAAK;;;;;;kGAEhB,uVAAC;wFAAE,WAAU;kGACV,QAAQ,WAAW;;;;;;;;;;;;;;;;;;mEAlBlB;;;;;4DAwBd;;;;;;;;;;;;8DAIJ,uVAAC;oDAAI,WAAU;8DACb,cAAA,uVAAC;wDAAE,WAAU;kEAAmC;;;;;;;;;;;;;;;;;;;;;;2CAQxD,yBAAyB,iBACzB,uVAAC;oCAAI,WAAU;;sDAEb,uVAAC;4CAAI,WAAU;;8DACb,uVAAC;oDAAI,WAAU;;sEACb,uVAAC;4DAAI,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACf,gEACA,CAAC,kBAAkB,EAAE,KAAK,QAAQ,CAAC,kBAAkB,CAAC,EACtD,KAAK,KAAK;sEAET,KAAK,IAAI;;;;;;sEAEZ,uVAAC;;8EACC,uVAAC;oEAAG,WAAU;8EACX,KAAK,KAAK;;;;;;8EAEb,uVAAC;oEAAE,WAAU;8EACV,KAAK,QAAQ;;;;;;;;;;;;;;;;;;gDAKnB,KAAK,KAAK,kBACT,uVAAC;oDAAI,WAAU;;sEACb,uVAAC;4DAAE,WAAU;;8EACX,uVAAC;oEAAK,WAAU;;;;;;gEAA6C;;;;;;;sEAG/D,uVAAC;4DAAI,WAAU;sEACZ,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,0BACrB,uVAAC;oEAAqB,WAAU;8EAC7B;mEADQ;;;;;;;;;;;;;;;;8DAOnB,uVAAC;oDAAE,WAAU;;sEACX,uVAAC;4DAAK,WAAU;;;;;;wDAA4C;;;;;;;;;;;;;wCAM/D,KAAK,QAAQ,kBACZ,uVAAC;4CAAI,WAAU;sDACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC3B,uVAAC,2HAAA,CAAA,SAAM;oDAEL,SAAQ;oDACR,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,yGACA;oDAEF,SAAS,IAAM,mBAAmB,QAAQ,OAAO;;sEAEjD,uVAAC;4DAAI,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACf,sFACA,CAAC,iBAAiB,EAAE,KAAK,QAAQ,EAAE;;;;;;sEAErC,uVAAC;4DAAI,WAAU;;8EACb,uVAAC;oEAAI,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACf,+DACA,QAAQ,IAAI,KAAK,YAAY,+BAC3B,QAAQ,IAAI,KAAK,aAAa,iCAC5B;8EAEH,QAAQ,IAAI,KAAK,YAAY,MAC5B,QAAQ,IAAI,KAAK,aAAa,MAAM;;;;;;8EAExC,uVAAC;oEAAI,WAAU;;sFACb,uVAAC;4EAAK,WAAU;sFACb,QAAQ,KAAK;;;;;;sFAEhB,uVAAC;4EAAK,WAAU;sFACb,QAAQ,OAAO;;;;;;;;;;;;;;;;;;sEAItB,uVAAC;4DAAI,WAAU;sEACb,cAAA,uVAAC,sSAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;;;;;;;mDAhCnB;;;;;;;;;;;;;;;;;;;;;;uBA5Ld,KAAK,EAAE;;;;;;;;;;0BA0OlB,uVAAC,4HAAA,CAAA,UAAO;;kCACN,uVAAC,4HAAA,CAAA,iBAAc;wBAAC,OAAO;kCACrB,cAAA,uVAAC,2HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,8FACA,6GACA;4BAEF,SAAS;;8CAET,uVAAC;oCAAI,WAAU;;;;;;8CACf,uVAAC;oCAAI,WAAU;8CACb,cAAA,uVAAC,wTAAA,CAAA,oBAAiB;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAInC,uVAAC,4HAAA,CAAA,iBAAc;wBAAC,MAAK;wBAAS,WAAU;kCACtC,cAAA,uVAAC;4BAAI,WAAU;;8CACb,uVAAC;oCAAK,WAAU;8CAAgB;;;;;;8CAChC,uVAAC;oCAAK,WAAU;8CAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM5D", "debugId": null}}, {"offset": {"line": 2480, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/chat-map/chat-map-panel.tsx"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from \"@/components/ui/tabs\";\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport { useScrollToBottom } from \"@/lib/hooks/use-scroll-to-bottom\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport {\r\n  Maximize2,\r\n  Minimize2,\r\n  MessageSquare,\r\n  Layers,\r\n  Share2,\r\n  Users,\r\n} from \"lucide-react\";\r\nimport { TOC } from \"@/components/map/toc\";\r\n// import { Map } from \"@/types/map\"; // Replaced by @geon-map/odf types\r\nimport { UseMapReturn } from \"@geon-map/odf\";\r\nimport { Attachment, ChatRequestOptions, CreateMessage, Message } from \"ai\";\r\nimport { Dispatch, SetStateAction } from \"react\";\r\nimport { ChatMapInput } from \"@/components/chat-map/chat-map-input\";\r\nimport { Messages } from \"@/components/messages\";\r\nimport { Vote } from \"@/lib/db/schema\";\r\nimport { FooterText } from \"../footer\";\r\nimport { ServerStatus } from \"@/components/server-status\";\r\nimport { HelpMenuBar } from \"@/components/chat-map/help-menu-bar\";\r\n\r\nexport default function ChatMapPanel({\r\n  chatId,\r\n  messages,\r\n  isPanelCollapsed,\r\n  togglePanel,\r\n  isPanelMaximized,\r\n  toggleMaximize,\r\n  mapState,\r\n  input,\r\n  setInput,\r\n  handleSubmit,\r\n  stop,\r\n  attachments,\r\n  setAttachments,\r\n  setMessages,\r\n  append,\r\n  isReadonly,\r\n  reload,\r\n  votes,\r\n  status,\r\n  layerConfigs,\r\n  addToolResult,\r\n  error,\r\n  enableThinking,\r\n  setEnableThinking,\r\n  enableSmartNavigation,\r\n  setEnableSmartNavigation,\r\n  modelSupportsReasoning,\r\n  selectedModelId,\r\n}: {\r\n  chatId: string;\r\n  messages: Message[];\r\n  isPanelCollapsed?: boolean;\r\n  togglePanel?: () => void;\r\n  isPanelMaximized?: boolean;\r\n  toggleMaximize?: () => void;\r\n  mapState: UseMapReturn;\r\n  input: string;\r\n  setInput: (value: string) => void;\r\n  handleSubmit: (\r\n    event?: { preventDefault?: () => void },\r\n    chatRequestOptions?: ChatRequestOptions\r\n  ) => void;\r\n  stop: () => void;\r\n  attachments: Array<Attachment>;\r\n  setAttachments: Dispatch<SetStateAction<Array<Attachment>>>;\r\n  setMessages: Dispatch<SetStateAction<Array<Message>>>;\r\n  append: (\r\n    message: Message | CreateMessage,\r\n    chatRequestOptions?: ChatRequestOptions\r\n  ) => Promise<string | null | undefined>;\r\n  isReadonly: boolean;\r\n  reload: (\r\n    chatRequestOptions?: ChatRequestOptions\r\n  ) => Promise<string | null | undefined>;\r\n  votes: Array<Vote> | undefined;\r\n  status: \"submitted\" | \"streaming\" | \"ready\" | \"error\";\r\n  layerConfigs: any; // TODO: 타입을 LayerProps[]로 변경\r\n  addToolResult: ({\r\n    toolCallId,\r\n    result,\r\n  }: {\r\n    toolCallId: string;\r\n    result: string;\r\n  }) => void;\r\n  error: Error | undefined | null;\r\n  enableThinking: boolean;\r\n  setEnableThinking: Dispatch<SetStateAction<boolean>>;\r\n  enableSmartNavigation: boolean;\r\n  setEnableSmartNavigation: Dispatch<SetStateAction<boolean>>;\r\n  modelSupportsReasoning: boolean;\r\n  selectedModelId: string;\r\n}) {\r\n  const [activeTab, setActiveTab] = useState(\"chat\");\r\n  const [autoSave, setAutoSave] = useState(false);\r\n  const [messagesContainerRef, messagesEndRef] =\r\n    useScrollToBottom<HTMLDivElement>();\r\n\r\n  return (\r\n    <div\r\n      className={cn(\r\n        \"flex flex-col h-dvh bg-background\",\r\n        \"transition-all duration-300 ease-in-out\",\r\n        isPanelCollapsed ? \"hidden\" : \"w-auto\",\r\n        isPanelMaximized ? \"border-l border-border\" : \"rounded-l-lg\"\r\n      )}\r\n    >\r\n      {/* Header */}\r\n      <div className=\"flex items-center justify-between px-4 h-12 border-b border-border bg-secondary/5\">\r\n        {/* Left: Title and Server Status */}\r\n        <div className=\"flex items-center gap-3\">\r\n          {/* Server Status */}\r\n          <ServerStatus\r\n            selectedModelId={selectedModelId}\r\n            // className=\"ml-1\"\r\n          />\r\n        </div>\r\n\r\n        {/* Right: Controls */}\r\n        <div className=\"flex items-center gap-2\">\r\n\r\n          {/* <AutoSave\r\n            mapState={mapState}\r\n            enabled={autoSave}\r\n            onEnabledChange={setAutoSave}\r\n          />\r\n          <SaveButton mapState={mapState} /> */}\r\n\r\n          {/* <Tooltip>\r\n            <TooltipTrigger asChild>\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"icon\"\r\n                className=\"h-8 w-8\"\r\n                onClick={() => {}}\r\n                disabled={true}\r\n              >\r\n                <Share2 className=\"h-4 w-4\" />\r\n              </Button>\r\n            </TooltipTrigger>\r\n            <TooltipContent>Share Map</TooltipContent>\r\n          </Tooltip> */}\r\n\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            onClick={toggleMaximize}\r\n            className=\"h-8 w-8\"\r\n          >\r\n            {isPanelMaximized ? (\r\n              <Minimize2 className=\"h-4 w-4\" />\r\n            ) : (\r\n              <Maximize2 className=\"h-4 w-4\" />\r\n            )}\r\n          </Button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Tabs */}\r\n      <Tabs\r\n        value={activeTab}\r\n        onValueChange={setActiveTab}\r\n        className=\"flex-grow flex flex-col min-h-0\"\r\n      >\r\n        <TabsList className=\"flex p-1 gap-1 border-b border-border\">\r\n          <TabsTrigger\r\n            value=\"chat\"\r\n            className={cn(\r\n              \"flex-1 flex items-center justify-center gap-2\",\r\n              \"text-sm rounded-md transition-all\"\r\n            )}\r\n          >\r\n            <MessageSquare className=\"h-4 w-4\" />\r\n            AI 대화\r\n          </TabsTrigger>\r\n          <TabsTrigger\r\n            value=\"toc\"\r\n            className={cn(\r\n              \"flex-1 flex items-center justify-center gap-2\",\r\n              \"text-sm rounded-md transition-all\"\r\n            )}\r\n          >\r\n            <Layers className=\"h-4 w-4\" />\r\n            레이어\r\n          </TabsTrigger>\r\n        </TabsList>\r\n\r\n        <TabsContent\r\n          value=\"chat\"\r\n          className=\"flex flex-col h-full overflow-hidden data-[state=inactive]:hidden mt-0\"\r\n        >\r\n          <HelpMenuBar\r\n            setInput={setInput}\r\n            hasMessages={messages.length > 0}\r\n          />\r\n          <Messages\r\n            chatId={chatId}\r\n            setInput={setInput}\r\n            votes={votes}\r\n            messages={messages}\r\n            setMessages={setMessages}\r\n            reload={reload}\r\n            isReadonly={isReadonly}\r\n            status={status}\r\n            error={error} // error prop 전달\r\n            addToolResult={addToolResult}\r\n            mapState={mapState} // mapState 전달\r\n          />\r\n\r\n          <form className=\"flex mx-auto px-4 gap-2 w-full md:max-w-3xl\">\r\n            {!isReadonly && (\r\n              <ChatMapInput\r\n                chatId={chatId}\r\n                input={input}\r\n                setInput={setInput}\r\n                handleSubmit={handleSubmit}\r\n                status={status} // isLoading 대신 status 전달\r\n                stop={stop}\r\n                attachments={attachments}\r\n                setAttachments={setAttachments}\r\n                messages={messages}\r\n                setMessages={setMessages}\r\n                append={append}\r\n                enableThinking={enableThinking}\r\n                setEnableThinking={setEnableThinking}\r\n                enableSmartNavigation={enableSmartNavigation}\r\n                setEnableSmartNavigation={setEnableSmartNavigation}\r\n                modelSupportsReasoning={modelSupportsReasoning}\r\n                selectedModelId={selectedModelId}\r\n              />\r\n            )}\r\n          </form>\r\n          <FooterText className=\"py-2\" />\r\n        </TabsContent>\r\n\r\n        <TabsContent value=\"toc\" className=\"flex flex-col\">\r\n          <TOC map={mapState?.map} layers={layerConfigs || []} />\r\n        </TabsContent>\r\n      </Tabs>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAQA;AAKA;AACA;AAEA;AACA;AACA;;;;;;;;;;;;;;AAEe,SAAS,aAAa,EACnC,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,WAAW,EACX,gBAAgB,EAChB,cAAc,EACd,QAAQ,EACR,KAAK,EACL,QAAQ,EACR,YAAY,EACZ,IAAI,EACJ,WAAW,EACX,cAAc,EACd,WAAW,EACX,MAAM,EACN,UAAU,EACV,MAAM,EACN,KAAK,EACL,MAAM,EACN,YAAY,EACZ,aAAa,EACb,KAAK,EACL,cAAc,EACd,iBAAiB,EACjB,qBAAqB,EACrB,wBAAwB,EACxB,sBAAsB,EACtB,eAAe,EA4ChB;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,sBAAsB,eAAe,GAC1C,CAAA,GAAA,6IAAA,CAAA,oBAAiB,AAAD;IAElB,qBACE,uVAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,qCACA,2CACA,mBAAmB,WAAW,UAC9B,mBAAmB,2BAA2B;;0BAIhD,uVAAC;gBAAI,WAAU;;kCAEb,uVAAC;wBAAI,WAAU;kCAEb,cAAA,uVAAC,+HAAA,CAAA,eAAY;4BACX,iBAAiB;;;;;;;;;;;kCAMrB,uVAAC;wBAAI,WAAU;kCAwBb,cAAA,uVAAC,2HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;sCAET,iCACC,uVAAC,oSAAA,CAAA,YAAS;gCAAC,WAAU;;;;;qDAErB,uVAAC,oSAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAO7B,uVAAC,yHAAA,CAAA,OAAI;gBACH,OAAO;gBACP,eAAe;gBACf,WAAU;;kCAEV,uVAAC,yHAAA,CAAA,WAAQ;wBAAC,WAAU;;0CAClB,uVAAC,yHAAA,CAAA,cAAW;gCACV,OAAM;gCACN,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,iDACA;;kDAGF,uVAAC,4SAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAGvC,uVAAC,yHAAA,CAAA,cAAW;gCACV,OAAM;gCACN,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,iDACA;;kDAGF,uVAAC,0RAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;;kCAKlC,uVAAC,yHAAA,CAAA,cAAW;wBACV,OAAM;wBACN,WAAU;;0CAEV,uVAAC,iJAAA,CAAA,cAAW;gCACV,UAAU;gCACV,aAAa,SAAS,MAAM,GAAG;;;;;;0CAEjC,uVAAC,uHAAA,CAAA,WAAQ;gCACP,QAAQ;gCACR,UAAU;gCACV,OAAO;gCACP,UAAU;gCACV,aAAa;gCACb,QAAQ;gCACR,YAAY;gCACZ,QAAQ;gCACR,OAAO;gCACP,eAAe;gCACf,UAAU;;;;;;0CAGZ,uVAAC;gCAAK,WAAU;0CACb,CAAC,4BACA,uVAAC,kJAAA,CAAA,eAAY;oCACX,QAAQ;oCACR,OAAO;oCACP,UAAU;oCACV,cAAc;oCACd,QAAQ;oCACR,MAAM;oCACN,aAAa;oCACb,gBAAgB;oCAChB,UAAU;oCACV,aAAa;oCACb,QAAQ;oCACR,gBAAgB;oCAChB,mBAAmB;oCACnB,uBAAuB;oCACvB,0BAA0B;oCAC1B,wBAAwB;oCACxB,iBAAiB;;;;;;;;;;;0CAIvB,uVAAC,qHAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;;;;;;;kCAGxB,uVAAC,yHAAA,CAAA,cAAW;wBAAC,OAAM;wBAAM,WAAU;kCACjC,cAAA,uVAAC,yHAAA,CAAA,MAAG;4BAAC,KAAK,UAAU;4BAAK,QAAQ,gBAAgB,EAAE;;;;;;;;;;;;;;;;;;;;;;;AAK7D", "debugId": null}}, {"offset": {"line": 2729, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/chat-map/chat-map.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useEffect, useRef, useState } from \"react\";\r\nimport { useChat } from \"@ai-sdk/react\";\r\nimport { Attachment, Message, tool } from \"ai\";\r\nimport {\r\n  ResizableHandle,\r\n  ResizablePanel,\r\n  ResizablePanelGroup,\r\n} from \"@/components/ui/resizable\";\r\nimport { MapSidebar } from \"@/components/map/map-sidebar\";\r\nimport { MapControls } from \"@/components/map/map-controls\";\r\nimport ChatMapPanel from \"@/components/chat-map/chat-map-panel\";\r\nimport { LocationPopup } from \"@/components/map/location-popup\";\r\nimport { OriginPopup } from \"@/components/map/origin-popup\";\r\nimport { DestinationPopup } from \"@/components/map/destination-popup\";\r\nimport { cn, fetcher } from \"@/lib/utils\";\r\nimport { ImperativePanelHandle } from \"react-resizable-panels\";\r\nimport {\r\n  MapProvider,\r\n  Layer,\r\n  type LayerProps,\r\n  LayerProvider,\r\n  MapContainer,\r\n  useMapSetup,\r\n  // MapState, // Assuming useMapSetup returns a more specific type like UseMapReturn\r\n} from \"@geon-map/odf\";\r\nimport type { UseChatHelpers, CreateMessage } from \"@ai-sdk/react\"; // Import helper types, removed ChatRequestOptions\r\n\r\nimport \"@/public/css/map.css\";\r\nimport useSWR, { useSWRConfig } from \"swr\";\r\n\r\n\r\nimport { Vote } from \"@/lib/db/schema\";\r\nimport { ToolInvocationProvider, useLocation } from \"@/providers/tool-invocation-provider\";\r\nimport { BasemapProvider } from \"@/providers/basemap-provider\";\r\nimport { useLayerConfigs, useLayerManager } from \"@/hooks/use-layer-configs\";\r\nimport { supportsReasoning } from \"@/lib/ai/models\";\r\n\r\nfunction PureChatMap({\r\n  id,\r\n  isReadOnly,\r\n  mapState,\r\n  mapContainerRef,\r\n  messages,\r\n  append,\r\n  reload,\r\n  stop,\r\n  input,\r\n  setInput,\r\n  handleSubmit,\r\n  status,\r\n  error,\r\n  addToolResult,\r\n  setMessages,\r\n  enableThinking,\r\n  setEnableThinking,\r\n  enableSmartNavigation,\r\n  setEnableSmartNavigation,\r\n  modelSupportsReasoning,\r\n  selectedModelId,\r\n  layerConfigsRef,\r\n}: {\r\n  id: string;\r\n  isReadOnly: boolean;\r\n  mapState: ReturnType<typeof useMapSetup>;\r\n  mapContainerRef: React.RefObject<HTMLDivElement>; // Allow null for initial ref value\r\n  messages: Message[];\r\n  append: UseChatHelpers[\"append\"];\r\n  reload: UseChatHelpers[\"reload\"];\r\n  stop: UseChatHelpers[\"stop\"];\r\n  input: UseChatHelpers[\"input\"];\r\n  setInput: UseChatHelpers[\"setInput\"];\r\n  handleSubmit: UseChatHelpers[\"handleSubmit\"];\r\n  status: UseChatHelpers[\"status\"];\r\n  error: UseChatHelpers[\"error\"];\r\n  addToolResult: ({\r\n    toolCallId,\r\n    result,\r\n  }: {\r\n    toolCallId: string;\r\n    result: any;\r\n  }) => void; // Keep explicit type if custom\r\n  setMessages: UseChatHelpers[\"setMessages\"];\r\n  enableThinking: boolean;\r\n  setEnableThinking: React.Dispatch<React.SetStateAction<boolean>>;\r\n  enableSmartNavigation: boolean;\r\n  setEnableSmartNavigation: React.Dispatch<React.SetStateAction<boolean>>;\r\n  modelSupportsReasoning: boolean;\r\n  selectedModelId: string;\r\n  layerConfigsRef: React.MutableRefObject<any[]>;\r\n}) {\r\n  const { mutate } = useSWRConfig();\r\n  const [isPanelCollapsed, setIsPanelCollapsed] = useState(false);\r\n  const [isPanelMaximized, setIsPanelMaximized] = useState(false);\r\n  const [isResizing, setIsResizing] = useState(false);\r\n  const isFirstMessage = useRef(true);\r\n\r\n  const { data: votes } = useSWR<Array<Vote>>(\r\n    `/api/vote?chatId=${id}`,\r\n    fetcher\r\n  );\r\n\r\n  const [attachments, setAttachments] = useState<Array<Attachment>>([]);\r\n\r\n  const togglePanel = () => setIsPanelCollapsed(!isPanelCollapsed);\r\n  const toggleMaximize = () => setIsPanelMaximized(!isPanelMaximized);\r\n  const rightPanelRef = useRef<ImperativePanelHandle | null>(null);\r\n\r\n  const layerConfigs = useLayerConfigs();\r\n  const layerManager = useLayerManager();\r\n\r\n  // layerConfigs를 ref에 업데이트\r\n  useEffect(() => {\r\n    layerConfigsRef.current = layerConfigs;\r\n  }, [layerConfigs, layerConfigsRef]);\r\n\r\n  // zIndex 변경을 감지하여 실제 지도 레이어에 적용\r\n  useEffect(() => {\r\n    if (mapState?.map && layerConfigs.length > 0) {\r\n      layerConfigs.forEach((layerConfig) => {\r\n        const { id, zIndex } = layerConfig as any;\r\n        if (id && zIndex !== undefined) {\r\n          try {\r\n            // ODF Map의 setZIndex 메서드 사용\r\n            if (mapState.map.setZIndex) {\r\n              mapState.map.setZIndex(id, zIndex);\r\n            }\r\n          } catch (error) {\r\n            console.error(`Failed to set zIndex for layer ${id}:`, error);\r\n          }\r\n        }\r\n      });\r\n    }\r\n  }, [layerConfigs]);\r\n\r\n  const {\r\n    currentLocation,\r\n    setCurrentLocation,\r\n    originPoint,\r\n    setOriginPoint,\r\n    destinationPoint,\r\n    setDestinationPoint\r\n  } = useLocation();\r\n\r\n  useEffect(() => {\r\n    if (\r\n      (status === \"submitted\" || status === \"streaming\") &&\r\n      isFirstMessage.current &&\r\n      messages.length === 1\r\n    ) {\r\n      setIsPanelCollapsed(false);\r\n      isFirstMessage.current = false;\r\n    }\r\n  }, [status, messages.length]);\r\n\r\n  useEffect(() => {\r\n    const panel = rightPanelRef.current;\r\n    if (panel) {\r\n      if (isPanelCollapsed) {\r\n        panel.resize(0);\r\n      } else if (isPanelMaximized) {\r\n        panel.resize(70);\r\n      } else {\r\n        panel.resize(30);\r\n      }\r\n    }\r\n  }, [isPanelCollapsed, isPanelMaximized]);\r\n\r\n  return (\r\n    <div className=\"flex flex-col h-full w-full overflow-hidden\">\r\n      <ResizablePanelGroup direction=\"horizontal\" className=\"h-full rounded-lg\">\r\n        <ResizablePanel order={1} defaultSize={99} minSize={50} maxSize={100}>\r\n          <MapProvider value={mapState}>\r\n            <div className=\"relative h-full w-full\">\r\n              <MapContainer\r\n                containerRef={mapContainerRef} // Use passed mapContainerRef\r\n                id={\"map\"}\r\n                className=\"absolute inset-0 h-full w-full\"\r\n              />\r\n              {mapState.map && layerConfigs.length > 0 && (\r\n                <LayerProvider>\r\n                  {layerConfigs.map((layerConfig, index) => {\r\n                    return (\r\n                      <Layer\r\n                        key={layerConfig.id || `layer-${index}`}\r\n                        {...layerConfig}\r\n                      />\r\n                    );\r\n                  })}\r\n                </LayerProvider>\r\n              )}\r\n              {/* Map Sidebar - 왼쪽 상단에 새 대화 버튼 */}\r\n              {/* <MapSidebar /> */}\r\n\r\n              {/* Map Controls - 우측 상단에 레이어 검색 + 배경지도 버튼 */}\r\n              {mapState.map && (\r\n                <MapControls mapState={mapState} />\r\n              )}\r\n\r\n              {/* 위치 팝업 */}\r\n              {currentLocation && (\r\n                <LocationPopup\r\n                  position={currentLocation.projectedCoord}\r\n                  latitude={currentLocation.latitude}\r\n                  longitude={currentLocation.longitude}\r\n                  accuracy={currentLocation.accuracy}\r\n                  timestamp={currentLocation.timestamp}\r\n                  onClose={() => setCurrentLocation(null)}\r\n                />\r\n              )}\r\n\r\n              {/* 출발지 팝업 */}\r\n              {originPoint && (\r\n                <OriginPopup\r\n                  position={originPoint.projectedCoord}\r\n                  address={originPoint.address}\r\n                  onClose={() => setOriginPoint(null)}\r\n                />\r\n              )}\r\n\r\n              {/* 목적지 팝업 */}\r\n              {destinationPoint && (\r\n                <DestinationPopup\r\n                  position={destinationPoint.projectedCoord}\r\n                  address={destinationPoint.address}\r\n                  onClose={() => setDestinationPoint(null)}\r\n                />\r\n              )}\r\n            </div>\r\n          </MapProvider>\r\n        </ResizablePanel>\r\n\r\n        <ResizableHandle\r\n          onDoubleClick={togglePanel}\r\n          onDragging={(e) => setIsResizing(e)}\r\n          withHandle\r\n        />\r\n\r\n        <ResizablePanel\r\n          ref={rightPanelRef}\r\n          order={2}\r\n          defaultSize={1}\r\n          minSize={1}\r\n          maxSize={50}\r\n          collapsible={isPanelCollapsed}\r\n          collapsedSize={1}\r\n          onCollapse={() => setIsPanelCollapsed(true)}\r\n          onExpand={() => setIsPanelCollapsed(false)}\r\n          className={cn(\r\n            !isResizing && \"transition-all duration-200 ease-in-out\"\r\n          )}\r\n        >\r\n          <ChatMapPanel\r\n            chatId={id}\r\n            messages={messages}\r\n            stop={stop}\r\n            reload={reload}\r\n            isPanelCollapsed={isPanelCollapsed}\r\n            togglePanel={togglePanel}\r\n            isPanelMaximized={isPanelMaximized}\r\n            toggleMaximize={toggleMaximize}\r\n            mapState={mapState}\r\n            input={input}\r\n            setInput={setInput}\r\n            handleSubmit={handleSubmit}\r\n            setMessages={setMessages}\r\n            setAttachments={setAttachments}\r\n            attachments={attachments}\r\n            votes={votes}\r\n            isReadonly={isReadOnly}\r\n            append={append}\r\n            status={status}\r\n            addToolResult={addToolResult}\r\n            error={error}\r\n            layerConfigs={layerConfigs}\r\n            enableThinking={enableThinking}\r\n            setEnableThinking={setEnableThinking}\r\n            enableSmartNavigation={enableSmartNavigation}\r\n            setEnableSmartNavigation={setEnableSmartNavigation}\r\n            modelSupportsReasoning={modelSupportsReasoning}\r\n            selectedModelId={selectedModelId}\r\n          />\r\n        </ResizablePanel>\r\n      </ResizablePanelGroup>\r\n    </div>\r\n  );\r\n}\r\nexport function ChatMap({\r\n  id,\r\n  initialMessages,\r\n  selectedModelId,\r\n  selectedVisibilityType, // Keep for now, might be used in useChat body\r\n  isReadOnly,\r\n}: {\r\n  id: string;\r\n  initialMessages: Message[];\r\n  selectedModelId: string;\r\n  selectedVisibilityType: string;\r\n  isReadOnly: boolean;\r\n}) {\r\n  const containerRef = useRef<HTMLDivElement>(null);\r\n  const layerConfigsRef = useRef<any[]>([]);\r\n  const mapState = useMapSetup({\r\n    containerRef: containerRef as React.RefObject<HTMLDivElement>, // Removed 'as React.RefObject<HTMLDivElement>' assertion\r\n    autoInit: true,\r\n  });\r\n  const { mutate } = useSWRConfig();\r\n  const modelSupportsReasoning = supportsReasoning(selectedModelId);\r\n  const [enableThinking, setEnableThinking] = useState(true);\r\n  const [enableSmartNavigation, setEnableSmartNavigation] = useState(true);\r\n\r\n  // 모델이 변경될 때 추론 설정을 자동으로 조정\r\n  useEffect(() => {\r\n    if (!modelSupportsReasoning) {\r\n      setEnableThinking(false);\r\n    } else {\r\n      setEnableThinking(true);\r\n    }\r\n  }, [modelSupportsReasoning]);\r\n\r\n  const {\r\n    messages,\r\n    append,\r\n    reload,\r\n    stop,\r\n    input,\r\n    setInput,\r\n    handleSubmit,\r\n    status,\r\n    error,\r\n    addToolResult,\r\n    setMessages,\r\n  } = useChat({\r\n    initialMessages,\r\n    body: {\r\n      id,\r\n      modelId: selectedModelId,\r\n      layers: layerConfigsRef.current,\r\n      enable_thinking: enableThinking,\r\n      enable_smart_navigation: enableSmartNavigation,\r\n    },\r\n    maxSteps: 5,\r\n    sendExtraMessageFields: true, // AI SDK 공식 권장: id와 createdAt 필드 전송\r\n    onFinish: () => {\r\n      mutate(\"/api/history\");\r\n    },\r\n  });\r\n\r\n  return (\r\n    <BasemapProvider mapState={mapState}>\r\n      <ToolInvocationProvider\r\n        messages={messages}\r\n        enableSmartNavigation={enableSmartNavigation}\r\n        mapState={mapState}\r\n      >\r\n        <PureChatMap\r\n          id={id}\r\n          isReadOnly={isReadOnly}\r\n          mapState={mapState}\r\n          mapContainerRef={containerRef as React.RefObject<HTMLDivElement>} // Pass containerRef here\r\n          messages={messages}\r\n          append={append}\r\n          reload={reload}\r\n          stop={stop}\r\n          input={input}\r\n          setInput={setInput}\r\n          handleSubmit={handleSubmit}\r\n          status={status}\r\n          error={error}\r\n          addToolResult={addToolResult}\r\n          setMessages={setMessages}\r\n          enableThinking={enableThinking}\r\n          setEnableThinking={setEnableThinking}\r\n          enableSmartNavigation={enableSmartNavigation}\r\n          setEnableSmartNavigation={setEnableSmartNavigation}\r\n          modelSupportsReasoning={modelSupportsReasoning}\r\n          selectedModelId={selectedModelId}\r\n          layerConfigsRef={layerConfigsRef}\r\n        />\r\n      </ToolInvocationProvider>\r\n    </BasemapProvider>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AAMA;AACA;AACA;AACA;AACA;AACA;AAEA;AAYA;AAAA;AAAA;AAIA;AACA;AACA;AACA;AArCA;;;;;;;;;;;;;;;;;;AAuCA,SAAS,YAAY,EACnB,EAAE,EACF,UAAU,EACV,QAAQ,EACR,eAAe,EACf,QAAQ,EACR,MAAM,EACN,MAAM,EACN,IAAI,EACJ,KAAK,EACL,QAAQ,EACR,YAAY,EACZ,MAAM,EACN,KAAK,EACL,aAAa,EACb,WAAW,EACX,cAAc,EACd,iBAAiB,EACjB,qBAAqB,EACrB,wBAAwB,EACxB,sBAAsB,EACtB,eAAe,EACf,eAAe,EA8BhB;IACC,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,wNAAA,CAAA,eAAY,AAAD;IAC9B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,iBAAiB,CAAA,GAAA,8SAAA,CAAA,SAAM,AAAD,EAAE;IAE9B,MAAM,EAAE,MAAM,KAAK,EAAE,GAAG,CAAA,GAAA,mOAAA,CAAA,UAAM,AAAD,EAC3B,CAAC,iBAAiB,EAAE,IAAI,EACxB,4GAAA,CAAA,UAAO;IAGT,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IAEpE,MAAM,cAAc,IAAM,oBAAoB,CAAC;IAC/C,MAAM,iBAAiB,IAAM,oBAAoB,CAAC;IAClD,MAAM,gBAAgB,CAAA,GAAA,8SAAA,CAAA,SAAM,AAAD,EAAgC;IAE3D,MAAM,eAAe,CAAA,GAAA,gIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,eAAe,CAAA,GAAA,gIAAA,CAAA,kBAAe,AAAD;IAEnC,0BAA0B;IAC1B,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EAAE;QACR,gBAAgB,OAAO,GAAG;IAC5B,GAAG;QAAC;QAAc;KAAgB;IAElC,gCAAgC;IAChC,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,OAAO,aAAa,MAAM,GAAG,GAAG;YAC5C,aAAa,OAAO,CAAC,CAAC;gBACpB,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG;gBACvB,IAAI,MAAM,WAAW,WAAW;oBAC9B,IAAI;wBACF,4BAA4B;wBAC5B,IAAI,SAAS,GAAG,CAAC,SAAS,EAAE;4BAC1B,SAAS,GAAG,CAAC,SAAS,CAAC,IAAI;wBAC7B;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,CAAC,+BAA+B,EAAE,GAAG,CAAC,CAAC,EAAE;oBACzD;gBACF;YACF;QACF;IACF,GAAG;QAAC;KAAa;IAEjB,MAAM,EACJ,eAAe,EACf,kBAAkB,EAClB,WAAW,EACX,cAAc,EACd,gBAAgB,EAChB,mBAAmB,EACpB,GAAG,CAAA,GAAA,4IAAA,CAAA,cAAW,AAAD;IAEd,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EAAE;QACR,IACE,CAAC,WAAW,eAAe,WAAW,WAAW,KACjD,eAAe,OAAO,IACtB,SAAS,MAAM,KAAK,GACpB;YACA,oBAAoB;YACpB,eAAe,OAAO,GAAG;QAC3B;IACF,GAAG;QAAC;QAAQ,SAAS,MAAM;KAAC;IAE5B,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,cAAc,OAAO;QACnC,IAAI,OAAO;YACT,IAAI,kBAAkB;gBACpB,MAAM,MAAM,CAAC;YACf,OAAO,IAAI,kBAAkB;gBAC3B,MAAM,MAAM,CAAC;YACf,OAAO;gBACL,MAAM,MAAM,CAAC;YACf;QACF;IACF,GAAG;QAAC;QAAkB;KAAiB;IAEvC,qBACE,uVAAC;QAAI,WAAU;kBACb,cAAA,uVAAC,8HAAA,CAAA,sBAAmB;YAAC,WAAU;YAAa,WAAU;;8BACpD,uVAAC,8HAAA,CAAA,iBAAc;oBAAC,OAAO;oBAAG,aAAa;oBAAI,SAAS;oBAAI,SAAS;8BAC/D,cAAA,uVAAC,6OAAA,CAAA,cAAW;wBAAC,OAAO;kCAClB,cAAA,uVAAC;4BAAI,WAAU;;8CACb,uVAAC,6OAAA,CAAA,eAAY;oCACX,cAAc;oCACd,IAAI;oCACJ,WAAU;;;;;;gCAEX,SAAS,GAAG,IAAI,aAAa,MAAM,GAAG,mBACrC,uVAAC,6OAAA,CAAA,gBAAa;8CACX,aAAa,GAAG,CAAC,CAAC,aAAa;wCAC9B,qBACE,uVAAC,6OAAA,CAAA,QAAK;4CAEH,GAAG,WAAW;2CADV,YAAY,EAAE,IAAI,CAAC,MAAM,EAAE,OAAO;;;;;oCAI7C;;;;;;gCAOH,SAAS,GAAG,kBACX,uVAAC,qIAAA,CAAA,cAAW;oCAAC,UAAU;;;;;;gCAIxB,iCACC,uVAAC,uIAAA,CAAA,gBAAa;oCACZ,UAAU,gBAAgB,cAAc;oCACxC,UAAU,gBAAgB,QAAQ;oCAClC,WAAW,gBAAgB,SAAS;oCACpC,UAAU,gBAAgB,QAAQ;oCAClC,WAAW,gBAAgB,SAAS;oCACpC,SAAS,IAAM,mBAAmB;;;;;;gCAKrC,6BACC,uVAAC,qIAAA,CAAA,cAAW;oCACV,UAAU,YAAY,cAAc;oCACpC,SAAS,YAAY,OAAO;oCAC5B,SAAS,IAAM,eAAe;;;;;;gCAKjC,kCACC,uVAAC,0IAAA,CAAA,mBAAgB;oCACf,UAAU,iBAAiB,cAAc;oCACzC,SAAS,iBAAiB,OAAO;oCACjC,SAAS,IAAM,oBAAoB;;;;;;;;;;;;;;;;;;;;;;8BAO7C,uVAAC,8HAAA,CAAA,kBAAe;oBACd,eAAe;oBACf,YAAY,CAAC,IAAM,cAAc;oBACjC,UAAU;;;;;;8BAGZ,uVAAC,8HAAA,CAAA,iBAAc;oBACb,KAAK;oBACL,OAAO;oBACP,aAAa;oBACb,SAAS;oBACT,SAAS;oBACT,aAAa;oBACb,eAAe;oBACf,YAAY,IAAM,oBAAoB;oBACtC,UAAU,IAAM,oBAAoB;oBACpC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,CAAC,cAAc;8BAGjB,cAAA,uVAAC,kJAAA,CAAA,UAAY;wBACX,QAAQ;wBACR,UAAU;wBACV,MAAM;wBACN,QAAQ;wBACR,kBAAkB;wBAClB,aAAa;wBACb,kBAAkB;wBAClB,gBAAgB;wBAChB,UAAU;wBACV,OAAO;wBACP,UAAU;wBACV,cAAc;wBACd,aAAa;wBACb,gBAAgB;wBAChB,aAAa;wBACb,OAAO;wBACP,YAAY;wBACZ,QAAQ;wBACR,QAAQ;wBACR,eAAe;wBACf,OAAO;wBACP,cAAc;wBACd,gBAAgB;wBAChB,mBAAmB;wBACnB,uBAAuB;wBACvB,0BAA0B;wBAC1B,wBAAwB;wBACxB,iBAAiB;;;;;;;;;;;;;;;;;;;;;;AAM7B;AACO,SAAS,QAAQ,EACtB,EAAE,EACF,eAAe,EACf,eAAe,EACf,sBAAsB,EACtB,UAAU,EAOX;IACC,MAAM,eAAe,CAAA,GAAA,8SAAA,CAAA,SAAM,AAAD,EAAkB;IAC5C,MAAM,kBAAkB,CAAA,GAAA,8SAAA,CAAA,SAAM,AAAD,EAAS,EAAE;IACxC,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,cAAW,AAAD,EAAE;QAC3B,cAAc;QACd,UAAU;IACZ;IACA,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,wNAAA,CAAA,eAAY,AAAD;IAC9B,MAAM,yBAAyB,CAAA,GAAA,mHAAA,CAAA,oBAAiB,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IAEnE,2BAA2B;IAC3B,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,wBAAwB;YAC3B,kBAAkB;QACpB,OAAO;YACL,kBAAkB;QACpB;IACF,GAAG;QAAC;KAAuB;IAE3B,MAAM,EACJ,QAAQ,EACR,MAAM,EACN,MAAM,EACN,IAAI,EACJ,KAAK,EACL,QAAQ,EACR,YAAY,EACZ,MAAM,EACN,KAAK,EACL,aAAa,EACb,WAAW,EACZ,GAAG,CAAA,GAAA,kQAAA,CAAA,UAAO,AAAD,EAAE;QACV;QACA,MAAM;YACJ;YACA,SAAS;YACT,QAAQ,gBAAgB,OAAO;YAC/B,iBAAiB;YACjB,yBAAyB;QAC3B;QACA,UAAU;QACV,wBAAwB;QACxB,UAAU;YACR,OAAO;QACT;IACF;IAEA,qBACE,uVAAC,iIAAA,CAAA,kBAAe;QAAC,UAAU;kBACzB,cAAA,uVAAC,4IAAA,CAAA,yBAAsB;YACrB,UAAU;YACV,uBAAuB;YACvB,UAAU;sBAEV,cAAA,uVAAC;gBACC,IAAI;gBACJ,YAAY;gBACZ,UAAU;gBACV,iBAAiB;gBACjB,UAAU;gBACV,QAAQ;gBACR,QAAQ;gBACR,MAAM;gBACN,OAAO;gBACP,UAAU;gBACV,cAAc;gBACd,QAAQ;gBACR,OAAO;gBACP,eAAe;gBACf,aAAa;gBACb,gBAAgB;gBAChB,mBAAmB;gBACnB,uBAAuB;gBACvB,0BAA0B;gBAC1B,wBAAwB;gBACxB,iBAAiB;gBACjB,iBAAiB;;;;;;;;;;;;;;;;AAK3B", "debugId": null}}]}