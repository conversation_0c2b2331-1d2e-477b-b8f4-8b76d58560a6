"use client";

import { isToday, isYesterday, subMonths, subWeeks } from "date-fns";
import Link from "next/link";
import { useParams, usePathname, useRouter } from "next/navigation";
import { type User } from "next-auth";
import { memo, useEffect, useState } from "react";
import { toast } from "sonner";
import useS<PERSON> from "swr";

import {
  ChevronRight,
  History,
  InfoIcon,
  MoreHorizontalIcon,
  TrashIcon,
} from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuAction,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubAction,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  useSidebar,
} from "@/components/ui/sidebar";
import { Chat } from "@/lib/db/schema";
import { cn, fetcher } from "@/lib/utils";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "./ui/collapsible";

type GroupedChats = {
  today: Chat[];
  yesterday: Chat[];
  lastWeek: Chat[];
  lastMonth: Chat[];
  older: Chat[];
};

const PureChatItem = ({
  chat,
  isActive,
  onDelete,
  setOpenMobile,
}: {
  chat: Chat;
  isActive: boolean;
  onDelete: (chatId: string) => void;
  setOpenMobile: (open: boolean) => void;
}) => (
  <SidebarMenuSubItem>
    <SidebarMenuSubButton
      asChild
      isActive={isActive}
      className="peer/menu-sub-button"
    >
      <Link
        href={`/geon-2d-map/${chat.id}`}
        onClick={() => setOpenMobile(false)}
      >
        <span>{chat.title}</span>
      </Link>
    </SidebarMenuSubButton>
    <DropdownMenu modal={true}>
      <DropdownMenuTrigger asChild>
        <SidebarMenuSubAction showOnHover>
          <MoreHorizontalIcon />
          <span className="sr-only">More</span>
        </SidebarMenuSubAction>
      </DropdownMenuTrigger>
      <DropdownMenuContent side="bottom" align="end">
        <DropdownMenuItem
          className="cursor-pointer text-destructive focus:bg-destructive/15 focus:text-destructive dark:text-red-500"
          onSelect={() => onDelete(chat.id)}
        >
          <TrashIcon className="mr-2 h-4 w-4" />
          <span>삭제</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  </SidebarMenuSubItem>
);

export const ChatItem = memo(PureChatItem, (prevProps, nextProps) => {
  if (prevProps.isActive !== nextProps.isActive) return false;
  return true;
});

export function SidebarHistory({ user }: { user: User | undefined }) {
  const { setOpenMobile } = useSidebar();
  const { id } = useParams();
  const pathname = usePathname();
  const {
    data: history,
    isLoading,
    mutate,
  } = useSWR<Array<Chat>>(user ? "/api/history" : null, fetcher, {
    fallbackData: [],
  });

  useEffect(() => {
    mutate();
  }, [pathname, mutate]);

  const [deleteId, setDeleteId] = useState<string | null>(null);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const router = useRouter();
  const handleDelete = async () => {
    const deletePromise = fetch(`/api/chat?id=${deleteId}`, {
      method: "DELETE",
    });

    toast.promise(deletePromise, {
      loading: "대화 삭제중...",
      success: () => {
        mutate((history) => {
          if (history) {
            return history.filter((h) => h.id !== id);
          }
        });
        return "대화가 삭제되었습니다.";
      },
      error: "대화 삭제 실패",
    });

    setShowDeleteDialog(false);

    if (deleteId === id) {
      router.push("/");
    }
  };

  if (!user) {
    return (
      <SidebarMenu>
        <SidebarMenuItem>
          <SidebarMenuButton>
            <History className="mr-2 h-4 w-4" />
            <span>대화 목록</span>
          </SidebarMenuButton>
          <SidebarMenuSub>
            <div className="text-zinc-500 w-full flex flex-row justify-center items-center text-sm gap-2 mt-2">
              <InfoIcon />
              <div>이전 대화를 저장하고 다시 보려면 로그인하세요!</div>
            </div>
          </SidebarMenuSub>
        </SidebarMenuItem>
      </SidebarMenu>
    );
  }

  if (isLoading) {
    return (
      <SidebarMenu>
        <SidebarMenuItem>
          <SidebarMenuButton>
            <History className="mr-2 h-4 w-4" />
            <span>대화 목록</span>
          </SidebarMenuButton>
          <SidebarMenuSub>
            <div className="flex flex-col">
              {[25, 80, 67, 26, 72].map((item) => (
                <div
                  key={item}
                  className="rounded-md h-8 flex gap-2 px-2 items-center"
                >
                  <div
                    className="h-4 rounded-md flex-1 max-w-[--skeleton-width] bg-sidebar-accent-foreground/10"
                    style={
                      {
                        "--skeleton-width": `${item}%`,
                      } as React.CSSProperties
                    }
                  />
                </div>
              ))}
            </div>
          </SidebarMenuSub>
        </SidebarMenuItem>
      </SidebarMenu>
    );
  }

  if (history?.length === 0) {
    return (
      <SidebarMenu>
        <SidebarMenuItem>
          <SidebarMenuButton>
            <History className="mr-2 h-4 w-4" />
            <span>대화 목록</span>
          </SidebarMenuButton>
          <SidebarMenuSub>
            <SidebarMenuSubItem>
              <SidebarMenuSubButton>
                <InfoIcon />
                <span>이전 대화가 없습니다.</span>
              </SidebarMenuSubButton>
            </SidebarMenuSubItem>
          </SidebarMenuSub>
        </SidebarMenuItem>
      </SidebarMenu>
    );
  }

  const groupChatsByDate = (chats: Chat[]): GroupedChats => {
    const now = new Date();
    const oneWeekAgo = subWeeks(now, 1);
    const oneMonthAgo = subMonths(now, 1);

    return chats.reduce(
      (groups, chat) => {
        const chatDate = new Date(chat.createdAt);

        if (isToday(chatDate)) {
          groups.today.push(chat);
        } else if (isYesterday(chatDate)) {
          groups.yesterday.push(chat);
        } else if (chatDate > oneWeekAgo) {
          groups.lastWeek.push(chat);
        } else if (chatDate > oneMonthAgo) {
          groups.lastMonth.push(chat);
        } else {
          groups.older.push(chat);
        }

        return groups;
      },
      {
        today: [],
        yesterday: [],
        lastWeek: [],
        lastMonth: [],
        older: [],
      } as GroupedChats
    );
  };

  return (
    <>
      <SidebarMenu>
        <Collapsible defaultOpen className="group/collapsible">
          <SidebarMenuItem>
            <CollapsibleTrigger asChild>
              <SidebarMenuButton>
                <History className="mr-2 h-4 w-4" />
                <span>대화 목록</span>
                <ChevronRight className="absolute right-2 h-4 w-4 transition-transform group-data-[state=open]/collapsible:rotate-90" />
              </SidebarMenuButton>
            </CollapsibleTrigger>

            <CollapsibleContent>
              <SidebarMenuSub className="mt-1">
                {history &&
                  (() => {
                    const groupedChats = groupChatsByDate(history);

                    return (
                      <>
                        {groupedChats.today.length > 0 && (
                          <>
                            <div className="px-2 py-1 text-xs text-sidebar-foreground/50">
                              오늘
                            </div>
                            {groupedChats.today.map((chat) => (
                              <ChatItem
                                key={chat.id}
                                chat={chat}
                                isActive={chat.id === id}
                                onDelete={(chatId) => {
                                  setDeleteId(chatId);
                                  setShowDeleteDialog(true);
                                }}
                                setOpenMobile={setOpenMobile}
                              />
                            ))}
                          </>
                        )}

                        {groupedChats.yesterday.length > 0 && (
                          <>
                            <div className="px-2 py-1 text-xs text-sidebar-foreground/50">
                              어제
                            </div>
                            {groupedChats.yesterday.map((chat) => (
                              <ChatItem
                                key={chat.id}
                                chat={chat}
                                isActive={chat.id === id}
                                onDelete={(chatId) => {
                                  setDeleteId(chatId);
                                  setShowDeleteDialog(true);
                                }}
                                setOpenMobile={setOpenMobile}
                              />
                            ))}
                          </>
                        )}

                        {groupedChats.lastWeek.length > 0 && (
                          <>
                            <div className="px-2 py-1 text-xs text-sidebar-foreground/50">
                              최근 7일
                            </div>
                            {groupedChats.lastWeek.map((chat) => (
                              <ChatItem
                                key={chat.id}
                                chat={chat}
                                isActive={chat.id === id}
                                onDelete={(chatId) => {
                                  setDeleteId(chatId);
                                  setShowDeleteDialog(true);
                                }}
                                setOpenMobile={setOpenMobile}
                              />
                            ))}
                          </>
                        )}

                        {groupedChats.lastMonth.length > 0 && (
                          <>
                            <div className="px-2 py-1 text-xs text-sidebar-foreground/50">
                              최근 30일
                            </div>
                            {groupedChats.lastMonth.map((chat) => (
                              <ChatItem
                                key={chat.id}
                                chat={chat}
                                isActive={chat.id === id}
                                onDelete={(chatId) => {
                                  setDeleteId(chatId);
                                  setShowDeleteDialog(true);
                                }}
                                setOpenMobile={setOpenMobile}
                              />
                            ))}
                          </>
                        )}

                        {groupedChats.older.length > 0 && (
                          <>
                            <div className="px-2 py-1 text-xs text-sidebar-foreground/50">
                              오래된 대화
                            </div>
                            {groupedChats.older.map((chat) => (
                              <ChatItem
                                key={chat.id}
                                chat={chat}
                                isActive={chat.id === id}
                                onDelete={(chatId) => {
                                  setDeleteId(chatId);
                                  setShowDeleteDialog(true);
                                }}
                                setOpenMobile={setOpenMobile}
                              />
                            ))}
                          </>
                        )}
                      </>
                    );
                  })()}
              </SidebarMenuSub>
            </CollapsibleContent>
          </SidebarMenuItem>
        </Collapsible>
      </SidebarMenu>

      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>대화 삭제</AlertDialogTitle>
            <AlertDialogDescription>
              이 작업은 되돌릴 수 없습니다. 정말로 삭제하시겠습니까?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>취소</AlertDialogCancel>
            <AlertDialogAction
             onClick={handleDelete}>삭제</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
