"use client";

import React, { useEffect, useRef, useState } from "react";
import { useChat } from "@ai-sdk/react";
import { Attachment, Message, tool } from "ai";
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from "@/components/ui/resizable";
import { MapSidebar } from "@/components/map/map-sidebar";
import { MapControls } from "@/components/map/map-controls";
import ChatMapPanel from "@/components/chat-map/chat-map-panel";
import { LocationPopup } from "@/components/map/location-popup";
import { OriginPopup } from "@/components/map/origin-popup";
import { DestinationPopup } from "@/components/map/destination-popup";
import { cn, fetcher } from "@/lib/utils";
import { ImperativePanelHandle } from "react-resizable-panels";
import {
  MapProvider,
  Layer,
  type LayerProps,
  LayerProvider,
  MapContainer,
  useMapSetup,
  // MapState, // Assuming useMapSetup returns a more specific type like UseMapReturn
} from "@geon-map/odf";
import type { UseChatHelpers, CreateMessage } from "@ai-sdk/react"; // Import helper types, removed ChatRequestOptions

import "@/public/css/map.css";
import useSWR, { useSWRConfig } from "swr";


import { Vote } from "@/lib/db/schema";
import { ToolInvocationProvider, useLocation } from "@/providers/tool-invocation-provider";
import { BasemapProvider } from "@/providers/basemap-provider";
import { useLayerConfigs, useLayerManager } from "@/hooks/use-layer-configs";
import { supportsReasoning } from "@/lib/ai/models";

function PureChatMap({
  id,
  isReadOnly,
  mapState,
  mapContainerRef,
  messages,
  append,
  reload,
  stop,
  input,
  setInput,
  handleSubmit,
  status,
  error,
  addToolResult,
  setMessages,
  enableThinking,
  setEnableThinking,
  enableSmartNavigation,
  setEnableSmartNavigation,
  modelSupportsReasoning,
  selectedModelId,
  layerConfigsRef,
}: {
  id: string;
  isReadOnly: boolean;
  mapState: ReturnType<typeof useMapSetup>;
  mapContainerRef: React.RefObject<HTMLDivElement>; // Allow null for initial ref value
  messages: Message[];
  append: UseChatHelpers["append"];
  reload: UseChatHelpers["reload"];
  stop: UseChatHelpers["stop"];
  input: UseChatHelpers["input"];
  setInput: UseChatHelpers["setInput"];
  handleSubmit: UseChatHelpers["handleSubmit"];
  status: UseChatHelpers["status"];
  error: UseChatHelpers["error"];
  addToolResult: ({
    toolCallId,
    result,
  }: {
    toolCallId: string;
    result: any;
  }) => void; // Keep explicit type if custom
  setMessages: UseChatHelpers["setMessages"];
  enableThinking: boolean;
  setEnableThinking: React.Dispatch<React.SetStateAction<boolean>>;
  enableSmartNavigation: boolean;
  setEnableSmartNavigation: React.Dispatch<React.SetStateAction<boolean>>;
  modelSupportsReasoning: boolean;
  selectedModelId: string;
  layerConfigsRef: React.MutableRefObject<any[]>;
}) {
  const { mutate } = useSWRConfig();
  const [isPanelCollapsed, setIsPanelCollapsed] = useState(false);
  const [isPanelMaximized, setIsPanelMaximized] = useState(false);
  const [isResizing, setIsResizing] = useState(false);
  const isFirstMessage = useRef(true);

  const { data: votes } = useSWR<Array<Vote>>(
    `/api/vote?chatId=${id}`,
    fetcher
  );

  const [attachments, setAttachments] = useState<Array<Attachment>>([]);

  const togglePanel = () => setIsPanelCollapsed(!isPanelCollapsed);
  const toggleMaximize = () => setIsPanelMaximized(!isPanelMaximized);
  const rightPanelRef = useRef<ImperativePanelHandle | null>(null);

  const layerConfigs = useLayerConfigs();
  const layerManager = useLayerManager();

  // layerConfigs를 ref에 업데이트
  useEffect(() => {
    layerConfigsRef.current = layerConfigs;
  }, [layerConfigs, layerConfigsRef]);

  // zIndex 변경을 감지하여 실제 지도 레이어에 적용
  useEffect(() => {
    if (mapState?.map && layerConfigs.length > 0) {
      layerConfigs.forEach((layerConfig) => {
        const { id, zIndex } = layerConfig as any;
        if (id && zIndex !== undefined) {
          try {
            // ODF Map의 setZIndex 메서드 사용
            if (mapState.map.setZIndex) {
              mapState.map.setZIndex(id, zIndex);
            }
          } catch (error) {
            console.error(`Failed to set zIndex for layer ${id}:`, error);
          }
        }
      });
    }
  }, [layerConfigs]);

  const {
    currentLocation,
    setCurrentLocation,
    originPoint,
    setOriginPoint,
    destinationPoint,
    setDestinationPoint
  } = useLocation();

  useEffect(() => {
    if (
      (status === "submitted" || status === "streaming") &&
      isFirstMessage.current &&
      messages.length === 1
    ) {
      setIsPanelCollapsed(false);
      isFirstMessage.current = false;
    }
  }, [status, messages.length]);

  useEffect(() => {
    const panel = rightPanelRef.current;
    if (panel) {
      if (isPanelCollapsed) {
        panel.resize(0);
      } else if (isPanelMaximized) {
        panel.resize(70);
      } else {
        panel.resize(30);
      }
    }
  }, [isPanelCollapsed, isPanelMaximized]);

  return (
    <div className="flex flex-col h-full w-full overflow-hidden">
      <ResizablePanelGroup direction="horizontal" className="h-full rounded-lg">
        <ResizablePanel order={1} defaultSize={99} minSize={50} maxSize={100}>
          <MapProvider value={mapState}>
            <div className="relative h-full w-full">
              <MapContainer
                containerRef={mapContainerRef} // Use passed mapContainerRef
                id={"map"}
                className="absolute inset-0 h-full w-full"
              />
              {mapState.map && layerConfigs.length > 0 && (
                <LayerProvider>
                  {layerConfigs.map((layerConfig, index) => {
                    return (
                      <Layer
                        key={layerConfig.id || `layer-${index}`}
                        {...layerConfig}
                      />
                    );
                  })}
                </LayerProvider>
              )}
              {/* Map Sidebar - 왼쪽 상단에 새 대화 버튼 */}
              {/* <MapSidebar /> */}

              {/* Map Controls - 우측 상단에 레이어 검색 + 배경지도 버튼 */}
              {mapState.map && (
                <MapControls mapState={mapState} />
              )}

              {/* 위치 팝업 */}
              {currentLocation && (
                <LocationPopup
                  position={currentLocation.projectedCoord}
                  latitude={currentLocation.latitude}
                  longitude={currentLocation.longitude}
                  accuracy={currentLocation.accuracy}
                  timestamp={currentLocation.timestamp}
                  onClose={() => setCurrentLocation(null)}
                />
              )}

              {/* 출발지 팝업 */}
              {originPoint && (
                <OriginPopup
                  position={originPoint.projectedCoord}
                  address={originPoint.address}
                  onClose={() => setOriginPoint(null)}
                />
              )}

              {/* 목적지 팝업 */}
              {destinationPoint && (
                <DestinationPopup
                  position={destinationPoint.projectedCoord}
                  address={destinationPoint.address}
                  onClose={() => setDestinationPoint(null)}
                />
              )}
            </div>
          </MapProvider>
        </ResizablePanel>

        <ResizableHandle
          onDoubleClick={togglePanel}
          onDragging={(e) => setIsResizing(e)}
          withHandle
        />

        <ResizablePanel
          ref={rightPanelRef}
          order={2}
          defaultSize={1}
          minSize={1}
          maxSize={50}
          collapsible={isPanelCollapsed}
          collapsedSize={1}
          onCollapse={() => setIsPanelCollapsed(true)}
          onExpand={() => setIsPanelCollapsed(false)}
          className={cn(
            !isResizing && "transition-all duration-200 ease-in-out"
          )}
        >
          <ChatMapPanel
            chatId={id}
            messages={messages}
            stop={stop}
            reload={reload}
            isPanelCollapsed={isPanelCollapsed}
            togglePanel={togglePanel}
            isPanelMaximized={isPanelMaximized}
            toggleMaximize={toggleMaximize}
            mapState={mapState}
            input={input}
            setInput={setInput}
            handleSubmit={handleSubmit}
            setMessages={setMessages}
            setAttachments={setAttachments}
            attachments={attachments}
            votes={votes}
            isReadonly={isReadOnly}
            append={append}
            status={status}
            addToolResult={addToolResult}
            error={error}
            layerConfigs={layerConfigs}
            enableThinking={enableThinking}
            setEnableThinking={setEnableThinking}
            enableSmartNavigation={enableSmartNavigation}
            setEnableSmartNavigation={setEnableSmartNavigation}
            modelSupportsReasoning={modelSupportsReasoning}
            selectedModelId={selectedModelId}
          />
        </ResizablePanel>
      </ResizablePanelGroup>
    </div>
  );
}
export function ChatMap({
  id,
  initialMessages,
  selectedModelId,
  selectedVisibilityType, // Keep for now, might be used in useChat body
  isReadOnly,
}: {
  id: string;
  initialMessages: Message[];
  selectedModelId: string;
  selectedVisibilityType: string;
  isReadOnly: boolean;
}) {
  const containerRef = useRef<HTMLDivElement>(null);
  const layerConfigsRef = useRef<any[]>([]);
  const mapState = useMapSetup({
    containerRef: containerRef as React.RefObject<HTMLDivElement>, // Removed 'as React.RefObject<HTMLDivElement>' assertion
    autoInit: true,
  });
  const { mutate } = useSWRConfig();
  const modelSupportsReasoning = supportsReasoning(selectedModelId);
  const [enableThinking, setEnableThinking] = useState(true);
  const [enableSmartNavigation, setEnableSmartNavigation] = useState(true);

  // 모델이 변경될 때 추론 설정을 자동으로 조정
  useEffect(() => {
    if (!modelSupportsReasoning) {
      setEnableThinking(false);
    } else {
      setEnableThinking(true);
    }
  }, [modelSupportsReasoning]);

  const {
    messages,
    append,
    reload,
    stop,
    input,
    setInput,
    handleSubmit,
    status,
    error,
    addToolResult,
    setMessages,
  } = useChat({
    initialMessages,
    body: {
      id,
      modelId: selectedModelId,
      layers: layerConfigsRef.current,
      enable_thinking: enableThinking,
      enable_smart_navigation: enableSmartNavigation,
    },
    maxSteps: 5,
    sendExtraMessageFields: true, // AI SDK 공식 권장: id와 createdAt 필드 전송
    onFinish: () => {
      mutate("/api/history");
    },
  });

  return (
    <BasemapProvider mapState={mapState}>
      <ToolInvocationProvider
        messages={messages}
        enableSmartNavigation={enableSmartNavigation}
        mapState={mapState}
      >
        <PureChatMap
          id={id}
          isReadOnly={isReadOnly}
          mapState={mapState}
          mapContainerRef={containerRef as React.RefObject<HTMLDivElement>} // Pass containerRef here
          messages={messages}
          append={append}
          reload={reload}
          stop={stop}
          input={input}
          setInput={setInput}
          handleSubmit={handleSubmit}
          status={status}
          error={error}
          addToolResult={addToolResult}
          setMessages={setMessages}
          enableThinking={enableThinking}
          setEnableThinking={setEnableThinking}
          enableSmartNavigation={enableSmartNavigation}
          setEnableSmartNavigation={setEnableSmartNavigation}
          modelSupportsReasoning={modelSupportsReasoning}
          selectedModelId={selectedModelId}
          layerConfigsRef={layerConfigsRef}
        />
      </ToolInvocationProvider>
    </BasemapProvider>
  );
}
