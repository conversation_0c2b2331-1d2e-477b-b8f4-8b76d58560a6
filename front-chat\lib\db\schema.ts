import { createId } from "@paralleldrive/cuid2";
import { InferSelectModel, relations } from "drizzle-orm";
import {
  boolean,
  index,
  integer,
  json,
  pgTable,
  primaryKey,
  text,
  timestamp,
  uuid,
  varchar,
} from "drizzle-orm/pg-core";
import { generateId } from "ai";

/**
 * 사용자, 인증, 인가 관련 테이블은 자유롭게 수정 가능합니다.
 * 현재 Chat 테이블 외에는 사용하지 않음.
 */
export const user = pgTable("user", {
  id: text("id")
    .primaryKey()
    .$defaultFn(() => createId()),
  name: text("name"),
  // if you are using Github OAuth, you can get rid of the username attribute (that is for Twitter OAuth)
  username: text("username"),
  gh_username: text("gh_username"),
  email: text("email").unique(),
  emailVerified: timestamp("emailVerified", { mode: "date" }),
  image: text("image"),
  createdAt: timestamp("createdAt", { mode: "date" }).defaultNow().notNull(),
  updatedAt: timestamp("updatedAt", { mode: "date" })
    .notNull()
    .$onUpdate(() => new Date()),
});

export const sessions = pgTable(
  "sessions",
  {
    sessionToken: text("sessionToken").primaryKey(),
    userId: text("userId")
      .notNull()
      .references(() => user.id, { onDelete: "cascade", onUpdate: "cascade" }),
    expires: timestamp("expires", { mode: "date" }).notNull(),
  },
  (table) => {
    return {
      userIdIdx: index().on(table.userId),
    };
  }
);

export const verificationTokens = pgTable(
  "verificationTokens",
  {
    identifier: text("identifier").notNull(),
    token: text("token").notNull().unique(),
    expires: timestamp("expires", { mode: "date" }).notNull(),
  },
  (table) => {
    return {
      compositePk: primaryKey({ columns: [table.identifier, table.token] }),
    };
  }
);

export const accounts = pgTable(
  "accounts",
  {
    userId: text("userId")
      .notNull()
      .references(() => user.id, { onDelete: "cascade", onUpdate: "cascade" }),
    type: text("type").notNull(),
    provider: text("provider").notNull(),
    providerAccountId: text("providerAccountId").notNull(),
    refresh_token: text("refresh_token"),
    refreshTokenExpiresIn: integer("refresh_token_expires_in"),
    access_token: text("access_token"),
    expires_at: integer("expires_at"),
    token_type: text("token_type"),
    scope: text("scope"),
    id_token: text("id_token"),
    session_state: text("session_state"),
    oauth_token_secret: text("oauth_token_secret"),
    oauth_token: text("oauth_token"),
  },
  (table) => {
    return {
      userIdIdx: index().on(table.userId),
      compositePk: primaryKey({
        columns: [table.provider, table.providerAccountId],
      }),
    };
  }
);

export type User = InferSelectModel<typeof user>;

export const chat = pgTable("Chat", {
  id: text("id").primaryKey(),
  createdAt: timestamp("createdAt").notNull(),
  title: text("title").notNull().default('New Chat'), // 기본값 추가
  userId: text("userId").notNull(),
  visibility: varchar("visibility", { enum: ["public", "private"] })
    .notNull()
    .default("private"),
  deletedAt: timestamp("deletedAt"), // 소프트 삭제를 위한 컬럼 (nullable)
});

export type Chat = InferSelectModel<typeof chat>;

export const message = pgTable("Message", {
  id: uuid("id").primaryKey().notNull().defaultRandom(),
  chatId: text("chatId")
    .notNull()
    .references(() => chat.id),
  role: varchar("role").notNull(),
  content: json("content"), // 기존 호환성을 위해 nullable로 변경
  parts: json("parts"), // AI SDK parts 배열
  attachments: json("attachments"), // AI SDK attachments 배열
  createdAt: timestamp("createdAt").notNull(),
  deletedAt: timestamp("deletedAt"), // 소프트 삭제를 위한 컬럼 (nullable)
  enableReasoning: boolean("enableReasoning"), // 추론 활성화 여부 (nullable, user 메시지에만 적용)
  modelId: text("modelId"), // 사용된 모델 ID (nullable, assistant 메시지에만 적용)
});

export type Message = InferSelectModel<typeof message>;

export const vote = pgTable(
  "Vote",
  {
    chatId: text("chatId")
      .notNull()
      .references(() => chat.id),
    messageId: uuid("messageId")
      .notNull()
      .references(() => message.id),
    isUpvoted: boolean("isUpvoted").notNull(),
  },
  (table) => {
    return {
      pk: primaryKey({ columns: [table.chatId, table.messageId] }),
    };
  }
);

export type Vote = InferSelectModel<typeof vote>;

export const map = pgTable("map", {
  id: text("id")
    .primaryKey()
    .notNull()
    .$defaultFn(() => generateId()),
  name: text("name").notNull(),
  createdBy: text("userId").notNull(),
  createdAt: timestamp("createdAt").notNull().defaultNow(),
  updatedAt: timestamp("updatedAt").notNull().defaultNow(),
  isPublic: boolean("isPublic").default(false),
  // 공유되는 핵심 상태
  layers: json("layers").notNull(), // Layer[]
  version: integer("version").notNull().default(1), // 동시성 제어를 위한 버전
});

// 사용자별 지도 뷰 상태
export const mapView = pgTable(
  "map_view",
  {
    id: text("id")
      .notNull()
      .$defaultFn(() => generateId()), // PRIMARY KEY 제거
    mapId: text("mapId")
      .notNull()
      .references(() => map.id, { onDelete: "cascade" }),
    userId: text("userId").notNull(),
    center: json("center").notNull(),
    zoom: integer("zoom").notNull(),
    basemap: text("basemap").notNull(),
    updatedAt: timestamp("updatedAt").notNull().defaultNow(),
  },
  (table) => {
    return {
      compoundKey: primaryKey({ columns: [table.mapId, table.userId] }), // 복합 키 설정
      idIdx: index("map_view_id_idx").on(table.id), // id는 unique index로 변경
    };
  }
);

// 채팅-지도 연결 테이블
export const chatMap = pgTable("chat_map", {
  id: text("id")
    .primaryKey()
    .notNull()
    .$defaultFn(() => generateId()),
  chatId: text("chatId")
    .notNull()
    .references(() => chat.id, { onDelete: "cascade" }),
  mapId: text("mapId")
    .notNull()
    .references(() => map.id, { onDelete: "restrict" }), // 지도는 보존
  createdAt: timestamp("createdAt").notNull().defaultNow(),
});

// 지도 접근 권한
export const mapAccess = pgTable("map_access", {
  id: text("id")
    .primaryKey()
    .notNull()
    .$defaultFn(() => generateId()),
  mapId: text("mapId")
    .notNull()
    .references(() => map.id, { onDelete: "cascade" }),
  userId: text("userId").notNull(),
  accessType: text("accessType").notNull(), // 'owner', 'edit', 'view'
  createdAt: timestamp("createdAt").notNull().defaultNow(),
});

// 실시간 협업 세션
export const mapSession = pgTable("map_session", {
  id: text("id")
    .primaryKey()
    .notNull()
    .$defaultFn(() => generateId()),
  mapId: text("mapId")
    .notNull()
    .references(() => map.id, { onDelete: "cascade" }),
  userId: text("userId").notNull(),
  isActive: boolean("isActive").default(true),
  lastActiveAt: timestamp("lastActiveAt").notNull().defaultNow(),
  // 선택적 view 동기화 설정
  syncView: boolean("syncView").default(false),
  followingUserId: text("followingUserId"), // 다른 사용자의 view를 따라갈 때
});

// 관계 설정
export const mapsRelation = relations(map, ({ many }) => ({
  views: many(mapView),
  access: many(mapAccess),
  sessions: many(mapSession),
  chats: many(chatMap),
}));

export const chatRelation = relations(chat, ({ many }) => ({
  maps: many(chatMap),
}));

export const sessionsRelations = relations(sessions, ({ one }) => ({
  user: one(user, { references: [user.id], fields: [sessions.userId] }),
}));

export const accountsRelations = relations(accounts, ({ one }) => ({
  user: one(user, { references: [user.id], fields: [accounts.userId] }),
}));

export const userRelations = relations(user, ({ many }) => ({
  accounts: many(accounts),
  sessions: many(sessions),
}));
