import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  SettingsIcon,
  MessageSquarePlus,
  MapPin,
  Layers3,
  BarChart3,
  Sparkles,
  ArrowRight,
  BadgeHelp,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { MagicCard } from "@/components/magicui/magic-card";
import { cn } from "@/lib/utils";

interface HelpMenuBarProps {
  setInput: (input: string) => void;
  hasMessages: boolean;
}

export const HelpMenuBar = ({ setInput }: HelpMenuBarProps) => {
  const [activePopover, setActivePopover] = useState<string | null>(null);
  const router = useRouter();

  const menuItems = [
    {
      id: "help",
      icon: <BadgeHelp className="w-5 h-5" />,
      title: "도움말",
      subtitle: "기능 안내",
      tooltip: "챗봇의 모든 기능을 한눈에 확인하세요",
      color: "text-blue-600",
      bgColor: "hover:bg-blue-50",
      gradient: "from-blue-500/10 to-blue-500/10",
      features: [
        {
          icon: "🗺️",
          title: "장소 검색 및 이동",
          description: "주소검색, 현재위치확인, 길찾기",
          category: "navigation"
        },
        {
          icon: "⚙️",
          title: "지도 제어",
          description: "상하좌우 이동, 줌레벨 변경, 배경지도 변경",
          category: "control"
        },
        {
          icon: "📚",
          title: "레이어 제어",
          description: "레이어 목록 조회, 상세 조회, 속성정보 조회, 스타일 변경, 레이어 제거",
          category: "layers"
        },
        {
          icon: "📊",
          title: "데이터 분석",
          description: "밀도 분석, 속성 필터",
          category: "analysis"
        }
      ]
    },
    {
      id: "search",
      icon: <MapPin className="w-5 h-5" />,
      title: "장소 검색",
      subtitle: "위치 찾기 & 길찾기",
      tooltip: "원하는 장소로 빠르게 이동하세요",
      color: "text-emerald-600",
      bgColor: "hover:bg-emerald-50",
      gradient: "from-emerald-500/10 to-green-500/10",
      tools: ["주소검색", "현재위치확인", "길찾기"],
      examples: [
        { label: "위치 이동", command: "웨이버스로 이동해줘", type: "secondary" },
        { label: "길찾기", command: "웨이버스에서 평촌역까지 얼마나 걸려?", type: "secondary" },
        { label: "현재위치 기반", command: "내 위치에서 구로디지털단지역까지 얼마나걸려?", type: "secondary" },
      ]
    },
    {
      id: "control",
      icon: <SettingsIcon className="w-5 h-5" />,
      title: "지도 제어",
      subtitle: "뷰포트 & 배경지도",
      tooltip: "지도 화면을 자유자재로 조작하세요",
      color: "text-orange-600",
      bgColor: "hover:bg-orange-50",
      gradient: "from-orange-500/10 to-amber-500/10",
      tools: ["상하좌우 이동", "줌레벨 변경", "배경지도 변경"],
      examples: [
        { label: "지도 확대", command: "지도를 확대해줘", type: "secondary" },
        { label: "지도 축소", command: "지도를 축소해줘", type: "secondary" },
        { label: "방향 이동", command: "위쪽으로 500m 이동해줘", type: "secondary" },
        { label: "배경지도 변경", command: "배경지도를 항공지도로 변경해줘", type: "secondary" }
      ]
    },
    {
      id: "layers",
      icon: <Layers3 className="w-5 h-5" />,
      title: "레이어 제어",
      subtitle: "데이터 시각화 & 스타일링",
      tooltip: "다양한 데이터 레이어를 관리하고 꾸며보세요",
      color: "text-purple-600",
      bgColor: "hover:bg-purple-50",
      gradient: "from-purple-500/10 to-violet-500/10",
      tools: ["레이어 목록 조회", "상세 조회", "속성정보 조회", "스타일 변경", "유형별 스타일 변경", "레이어 제거"],
      examples: [
        { label: "레이어 추가", command: "택지개발사업 레이어를 추가해줘", type: "secondary" },
        { label: "단일 스타일", command: "백년가게를 노란색 별모양으로 보여줄래?", type: "secondary" },
        { label: "조건부 스타일", command: "서울에 있는 약국만 빨간색으로 표시해줘", type: "secondary" },
        { label: "유형별 스타일", command: "서울의 건물을 5층까지 노란색, 10층까지 파란색, 나머지는 빨간색으로 표시해줄래?", type: "advanced" },
      ]
    },
    {
      id: "analysis",
      icon: <BarChart3 className="w-5 h-5" />,
      title: "데이터 분석",
      subtitle: "공간 분석 & 필터링",
      tooltip: "데이터에서 의미있는 패턴을 발견하세요",
      color: "text-rose-600",
      bgColor: "hover:bg-rose-50",
      gradient: "from-rose-500/10 to-pink-500/10",
      tools: ["밀도 분석", "속성 필터"],
      examples: [
        { label: "노후화 분석", command: "서울의 노후화된 건물을 보여줘", type: "advanced" },
        { label: "밀도 분석", command: "AI 발생농가 지역의 밀집도를 분석해줘", type: "advanced" },
      ]
    },
  ];

  const handleExampleClick = (command: string) => {
    setInput(command);
    setActivePopover(null);
  };

  const handleNewChat = () => {
    // 새 대화 시작 로직 (현재는 페이지 새로고침으로 구현)
    router.push('/');
    router.refresh();
  };

  // 메시지가 없을 때도 표시 (Overview와 함께 표시)
  return (
    <div className="flex items-center justify-between px-4 py-2 border-b border-border/60 bg-gradient-to-r from-secondary/30 via-background/50 to-secondary/20 backdrop-blur-sm">
      {/* 왼쪽: 기능 아이콘들 */}
      <div className="flex items-center gap-3">
        {menuItems.map((item) => (
          <Popover
            key={item.id}
            open={activePopover === item.id}
            onOpenChange={(open) => setActivePopover(open ? item.id : null)}
          >
            <Tooltip>
              <TooltipTrigger asChild>
                <PopoverTrigger asChild>
                  <Button
                    variant="secondary"
                    size="sm"
                    className={cn(
                      "h-10 w-10 p-0 transition-all duration-300 border rounded-xl relative overflow-hidden group",
                      "hover:shadow-md hover:scale-105 active:scale-95",
                      activePopover === item.id
                        ? `${item.color} bg-gradient-to-br ${item.gradient} border-current/30 shadow-lg scale-105`
                        : `${item.color} bg-background/80 border-border/40 hover:border-current/40 hover:bg-gradient-to-br hover:${item.gradient}`
                    )}
                  >
                    <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    <div className="relative">
                      {item.icon}
                    </div>
                  </Button>
                </PopoverTrigger>
              </TooltipTrigger>
              <TooltipContent side="bottom" className="text-xs font-medium bg-background/95 backdrop-blur-sm border border-border/40">
                <div className="flex flex-col items-center gap-1">
                  <span className="font-semibold">{item.title}</span>
                  <span className="text-muted-foreground text-xs">{item.subtitle}</span>
                </div>
              </TooltipContent>
            </Tooltip>
            <PopoverContent
              className={cn(
                "p-0 border shadow-xl backdrop-blur-xl",
                item.id === "help"
                  ? "w-[420px] border-slate-200/30 bg-gradient-to-br from-background/95 via-background/98 to-slate-50/20"
                  : "w-96 border-border/30 bg-gradient-to-br from-background/95 via-background/98 to-background/90"
              )}
              align="start"
              side="bottom"
              sideOffset={12}
            >
              {/* 도움말 아이콘인 경우 MagicCard로 래핑 */}
              {item.id === "help" ? (
                <MagicCard
                  className="p-5 bg-gradient-to-br from-background via-background/95 to-background/90"
                  gradientSize={100}
                  gradientColor={"#64748b"}
                  gradientOpacity={0.04}
                  gradientFrom={"#64748b"}
                  gradientTo={"#6b7280"}
                >
                  {/* 헤더 */}
                  <div className="flex items-center gap-3 mb-5">
                    <div className={cn(
                      "flex items-center justify-center w-10 h-10 rounded-xl border",
                      `bg-gradient-to-br ${item.gradient} border-current/20`,
                      item.color
                    )}>
                      {item.icon}
                    </div>
                    <div>
                      <h4 className="font-bold text-base text-foreground">
                        {item.title}
                      </h4>
                      <p className="text-xs text-muted-foreground">
                        {item.subtitle}
                      </p>
                    </div>
                  </div>
                  {/* 기능 안내 */}
                  {item.features && (
                    <div className="space-y-4">
                      <div className="bg-gradient-to-br from-primary/5 via-primary/2 to-primary/5 rounded-lg p-4 border border-primary/8">
                        <h5 className="font-semibold text-sm text-foreground mb-3 flex items-center gap-2">
                          <div className="w-2 h-2 bg-gradient-to-r from-primary to-primary/10 rounded-full"></div>
                          지원 기능
                        </h5>
                        <div className="grid gap-3">
                          {item.features.map((feature, index) => {
                            const IconComponent = {
                              navigation: MapPin,
                              control: SettingsIcon,
                              layers: Layers3,
                              analysis: BarChart3
                            }[feature.category];

                            const categoryColors: Record<string, string> = {
                              navigation: "text-emerald-600",
                              control: "text-orange-600",
                              layers: "text-purple-600",
                              analysis: "text-rose-600"
                            };

                            const categoryGradients: Record<string, string> = {
                              navigation: "from-emerald-500/10 to-green-500/10",
                              control: "from-orange-500/10 to-amber-500/10",
                              layers: "from-purple-500/10 to-violet-500/10",
                              analysis: "from-rose-500/10 to-pink-500/10"
                            };

                            return (
                              <div key={index} className="group">
                                <div className="flex items-center gap-3 p-3 bg-background/50 backdrop-blur-sm rounded-md border border-border/20 hover:border-primary/15 transition-all duration-200 hover:shadow-sm">
                                  <div className={cn(
                                    "flex items-center justify-center w-8 h-8 rounded-md border",
                                    `bg-gradient-to-br ${categoryGradients[feature.category]} border-current/20`,
                                    categoryColors[feature.category]
                                  )}>
                                    {IconComponent ? (
                                      <IconComponent className="w-4 h-4" />
                                    ) : (
                                      <span className="text-lg">{feature.icon}</span>
                                    )}
                                  </div>
                                  <div className="flex-1 min-w-0">
                                    <h6 className="font-medium text-xs text-foreground mb-0.5">
                                      {feature.title}
                                    </h6>
                                    <p className="text-xs text-muted-foreground leading-tight">
                                      {feature.description}
                                    </p>
                                  </div>
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      </div>

                      <div className="text-center pt-1 border-t border-border/15">
                        <p className="text-xs text-muted-foreground/80">
                          💬 자연어로 명령해보세요. 예: "강남역으로 가줘", "지도를 확대해줘"
                        </p>
                      </div>
                    </div>
                  )}
                </MagicCard>
              ) : (
                /* 일반 기능 아이콘인 경우 개선된 스타일 */
                <div className="p-5 space-y-4">
                  {/* 헤더 */}
                  <div className="border-b border-border/30 pb-4">
                    <div className="flex items-center gap-3 mb-3">
                      <div className={cn(
                        "flex items-center justify-center w-10 h-10 rounded-xl border",
                        `bg-gradient-to-br ${item.gradient} border-current/20`,
                        item.color
                      )}>
                        {item.icon}
                      </div>
                      <div>
                        <h4 className="font-bold text-base text-foreground">
                          {item.title}
                        </h4>
                        <p className="text-xs text-muted-foreground">
                          {item.subtitle}
                        </p>
                      </div>
                    </div>

                    {item.tools && (
                      <div className="bg-secondary/30 rounded-lg p-3 mb-3">
                        <p className="text-xs font-medium text-foreground/90 mb-2 flex items-center gap-2">
                          <span className="w-1.5 h-1.5 bg-primary rounded-full"></span>
                          지원 도구
                        </p>
                        <div className="flex flex-wrap gap-1">
                          {item.tools.map((tool, toolIndex) => (
                            <span key={toolIndex} className="inline-flex items-center px-2 py-1 rounded-md bg-background/60 border border-border/40 text-xs text-foreground/80">
                              {tool}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                    <p className="text-xs text-muted-foreground/80 flex items-center gap-2">
                      <span className="w-1 h-1 bg-primary/60 rounded-full"></span>
                      예시를 클릭하면 자동으로 입력됩니다
                    </p>
                  </div>

                  {/* 예시 목록 */}
                  {item.examples && (
                    <div className="space-y-2 max-h-72 overflow-y-auto styled-scrollbar">
                      {item.examples.map((example, exampleIndex) => (
                        <Button
                          key={exampleIndex}
                          variant="ghost"
                          className={cn(
                            "w-full group justify-between relative overflow-hidden rounded-lg p-2 h-auto border border-transparent",
                            "hover:border-primary/20 hover:shadow-sm transition-all duration-300"
                          )}
                          onClick={() => handleExampleClick(example.command)}
                        >
                          <div className={cn(
                            "absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300",
                            `bg-gradient-to-r ${item.gradient}`
                          )} />
                          <div className="relative flex items-center gap-3 text-left flex-1">
                            <div className={cn(
                              "flex items-center justify-center w-6 h-6 rounded-md text-xs",
                              example.type === 'primary' ? 'bg-primary/10 text-primary' :
                                example.type === 'advanced' ? 'bg-purple/10 text-purple-600' :
                                  'bg-secondary/50 text-muted-foreground'
                            )}>
                              {example.type === 'primary' ? '★' :
                                example.type === 'advanced' ? '◆' : '•'}
                            </div>
                            <div className="flex flex-col gap-1 flex-1 min-w-0">
                              <span className="text-sm font-medium text-foreground/90 group-hover:text-primary transition-colors">
                                {example.label}
                              </span>
                              <span className="text-xs text-muted-foreground/70 group-hover:text-muted-foreground transition-colors line-clamp-2">
                                {example.command}
                              </span>
                            </div>
                          </div>
                          <div className="relative opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <ArrowRight className="w-4 h-4 text-primary" />
                          </div>
                        </Button>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </PopoverContent>
          </Popover>
        ))}
      </div>

      {/* 오른쪽: 새 대화 버튼 */}
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="secondary"
            size="sm"
            className={cn(
              "h-10 w-10 p-0 transition-all duration-300 border rounded-xl relative overflow-hidden group",
              "bg-background/80 border-border/40 hover:border-primary/40 hover:shadow-md hover:scale-105 active:scale-95",
              "text-muted-foreground hover:text-primary hover:bg-gradient-to-br hover:from-primary/5 hover:to-primary/10"
            )}
            onClick={handleNewChat}
          >
            <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            <div className="relative">
              <MessageSquarePlus className="w-5 h-5" />
            </div>
          </Button>
        </TooltipTrigger>
        <TooltipContent side="bottom" className="text-xs font-medium bg-background/95 backdrop-blur-sm border border-border/40">
          <div className="flex flex-col items-center gap-1">
            <span className="font-semibold">새 대화</span>
            <span className="text-muted-foreground text-xs">대화 초기화</span>
          </div>
        </TooltipContent>
      </Tooltip>
    </div>
  );
};
